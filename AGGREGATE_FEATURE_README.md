# 汇总总结功能使用说明

## 功能概述

汇总总结功能允许插件收集来自多个群聊的总结内容，并将它们合并成一份综合性的汇总报告，发送到指定的目标群聊。

## 配置说明

### 基本配置

在插件配置文件中添加 `aggregate_summary` 配置项：

```json
{
  "aggregate_summary": {
    "enabled": true,
    "target_group_id": "999888777",
    "aggregate_prompt": "自定义汇总提示词",
    "source_groups": "[\"123456789\", \"987654321\", \"111222333\"]",
    "schedule_enabled": false,
    "schedule_cron": "0 23 * * *"
  }
}
```

### 配置项详解

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | boolean | false | 是否启用汇总功能 |
| `target_group_id` | string | "" | 接收汇总报告的目标群号 |
| `aggregate_prompt` | string | 默认提示词 | 用于生成汇总的LLM提示词 |
| `source_groups` | string | "[]" | JSON格式的源群号数组 |
| `schedule_enabled` | boolean | false | 是否启用定时汇总 |
| `schedule_cron` | string | "0 23 * * *" | 定时汇总的CRON表达式 |

## 使用方法

### 1. 启用汇总功能

在配置文件中设置：
```json
"aggregate_summary": {
  "enabled": true
}
```

### 2. 设置目标群

指定接收汇总报告的群号：
```json
"target_group_id": "999888777"
```

### 3. 配置源群列表

设置要汇总的源群列表（JSON数组格式）：
```json
"source_groups": "[\"123456789\", \"987654321\", \"111222333\"]"
```

### 4. 自定义汇总提示词

可以自定义LLM生成汇总时使用的提示词：
```json
"aggregate_prompt": "- Role: 多群聊汇总分析师\n- Background: 你需要将来自多个群聊的总结内容进行汇总整理..."
```

## 命令使用

### 手动触发汇总

使用命令：`汇总总结`

- 只有管理员可以使用此命令
- 会收集所有源群的最新总结
- 生成综合汇总报告
- 发送到配置的目标群

## 工作流程

1. **总结收集**：当源群中的任一群聊生成总结时，插件会自动存储该总结
2. **汇总触发**：通过手动命令或定时任务触发汇总
3. **内容整合**：收集所有源群的最新总结内容
4. **LLM处理**：使用配置的提示词让LLM生成综合汇总
5. **结果发送**：将汇总结果发送到目标群

## 注意事项

1. **权限要求**：只有管理员可以使用汇总功能
2. **存储限制**：每个群最多保留10条最新总结，避免内存占用过多
3. **群号格式**：source_groups 必须是有效的JSON数组格式
4. **LLM依赖**：需要配置有效的LLM提供商才能生成汇总

## 示例配置

参考 `example_aggregate_config.json` 文件获取完整的配置示例。

## 故障排除

### 常见问题

1. **汇总功能未启用**
   - 检查 `aggregate_summary.enabled` 是否为 true

2. **无权限使用**
   - 确保使用命令的用户具有管理员权限

3. **源群配置错误**
   - 检查 `source_groups` 是否为有效的JSON数组格式
   - 确保群号为字符串类型

4. **目标群未设置**
   - 检查 `target_group_id` 是否已正确配置

### 调试方法

1. 查看插件启动日志，确认汇总配置加载正常
2. 使用 `汇总总结` 命令测试功能
3. 检查是否有错误日志输出

## 更新日志

- v1.0.0: 初始版本，支持基本汇总功能
- 添加了配置验证和错误处理
- 支持自定义汇总提示词
- 添加了管理员权限检查
