# Dify 知识库集成功能应用完成

## 🎉 功能应用状态

✅ **所有 Dify 知识库集成功能已成功应用到代码中！**

## 📋 已应用的更改

### 1. 配置文件更新 ✅
**文件**: `_conf_schema.json`

已添加 Dify 相关配置项：
```json
{
  "dify_enabled": {
    "description": "是否启用Dify知识库上传",
    "type": "bool",
    "default": false
  },
  "dify_dataset_id": {
    "description": "Dify数据集ID", 
    "type": "string",
    "default": ""
  },
  "dify_token": {
    "description": "Dify API Token",
    "type": "string", 
    "default": ""
  }
}
```

### 2. 插件初始化更新 ✅
**文件**: `main.py` (第99-102行)

已添加 Dify 配置读取：
```python
# Dify 知识库配置
self.dify_enabled = self.schedule_config.get("dify_enabled", False)
self.dify_dataset_id = self.schedule_config.get("dify_dataset_id", "")
self.dify_token = self.schedule_config.get("dify_token", "")
```

### 3. Dify 上传方法 ✅
**文件**: `main.py` (第1175-1252行)

已添加完整的 Dify 知识库上传方法：
```python
async def post_to_dify_knowledge_base(self, content: str, title: str, dataset_id: str, token: str) -> bool:
    """发送内容到 Dify 知识库"""
    # 完整的实现包括：
    # - 临时文件创建和管理
    # - API 请求数据准备
    # - 高质量索引配置
    # - 错误处理和日志记录
```

### 4. 日报集成 ✅
**文件**: `main.py` (第648-664行)

已在日报生成后添加 Dify 上传：
```python
# 发送到 Dify 知识库
if self.dify_enabled and self.dify_dataset_id and self.dify_token:
    logger.info("Scheduler: Uploading aggregate summary to Dify knowledge base...")
    dify_title = f"{current_date_str}_PKMer社群日报"
    dify_success = await self.post_to_dify_knowledge_base(
        content=aggregate_summary,
        title=dify_title,
        dataset_id=self.dify_dataset_id,
        token=self.dify_token
    )
```

### 5. 周报集成 ✅
**文件**: `main.py` (第725-741行)

已在周报生成后添加 Dify 上传：
```python
# 发送到 Dify 知识库
if self.dify_enabled and self.dify_dataset_id and self.dify_token:
    logger.info("Scheduler: Uploading weekly summary to Dify knowledge base...")
    dify_weekly_title = f"PKMer社区周报_{current_date_str}"
    dify_weekly_success = await self.post_to_dify_knowledge_base(
        content=aggregate_weekly_summary,
        title=dify_weekly_title,
        dataset_id=self.dify_dataset_id,
        token=self.dify_token
    )
```

## 🔧 使用方法

### 配置步骤
1. **获取 Dify 凭据**：
   - 登录 Dify 控制台
   - 获取数据集ID和API Token

2. **更新配置文件**：
   ```json
   {
     "schedule": {
       "dify_enabled": true,
       "dify_dataset_id": "your_dataset_id_here",
       "dify_token": "your_api_token_here"
     }
   }
   ```

3. **重启插件**：
   - 重启 AstrBot 以加载新配置

### 自动上传
配置完成后，系统会自动：
- ✅ 日报生成后上传到 Dify 知识库
- ✅ 周报生成后上传到 Dify 知识库
- ✅ 与现有 Discourse 发送并行执行
- ✅ 详细的日志记录和错误处理

## 📤 API 调用详情

### 请求配置
- **URL**: `https://api.dify.ai/v1/datasets/{dataset_id}/document/create-by-file`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Authorization**: Bearer {token}

### 文档处理规则
```json
{
  "indexing_technique": "high_quality",
  "process_rule": {
    "rules": {
      "pre_processing_rules": [
        {"id": "remove_extra_spaces", "enabled": true},
        {"id": "remove_urls_emails", "enabled": false}
      ],
      "segmentation": {
        "separator": "##",
        "max_tokens": 2048
      }
    },
    "mode": "custom"
  },
  "doc_form": "hierarchical_model",
  "doc_type": "web_page"
}
```

## 🔄 完整工作流程

### 日报流程（增强版）
```
生成汇总 → 保存文件 → 上传 Dify → 发送群聊
```

### 周报流程（增强版）
```
生成周报 → 保存文件 → 上传 Dify → 发送群聊
```

## 📋 文档命名规则

- **日报**: `YYYY-MM-DD_PKMer社群日报`
- **周报**: `PKMer社区周报_YYYY-MM-DD`

## 🎯 功能特点

1. **双重发送**: Discourse + Dify 并行上传
2. **智能索引**: 使用 Dify 高质量索引技术
3. **自动管理**: 临时文件自动创建和清理
4. **错误隔离**: Dify 上传失败不影响其他功能
5. **详细日志**: 完整的操作日志记录

## 📊 日志示例

### 成功上传
```
[INFO] Scheduler: Uploading aggregate summary to Dify knowledge base...
[INFO] Successfully uploaded to Dify knowledge base: {"document_id": "xxx"}
[INFO] Scheduler: Successfully uploaded aggregate summary to Dify knowledge base.
```

### 配置检查
```
[INFO] Scheduler: Dify upload enabled, dataset_id: xxx, token configured
```

### 错误处理
```
[ERROR] Failed to upload to Dify knowledge base. Status: 401, Error: Unauthorized
[ERROR] Scheduler: Failed to upload aggregate summary to Dify knowledge base.
```

## ✅ 验证清单

- [x] 配置项已添加到 `_conf_schema.json`
- [x] 配置读取逻辑已实现
- [x] Dify 上传方法已添加
- [x] 日报集成已完成
- [x] 周报集成已完成
- [x] 错误处理已实现
- [x] 日志记录已添加
- [x] 临时文件管理已实现

## 🚀 立即可用

**所有更改已应用完成，Dify 知识库集成功能立即可用！**

只需：
1. 配置 `dify_enabled: true`
2. 设置 `dify_dataset_id` 和 `dify_token`
3. 重启插件

系统将自动开始将日报和周报上传到您的 Dify 知识库！

---

**总结**: Dify 知识库集成功能已完全应用到代码中，提供与 Discourse 论坛并行的知识库上传能力，支持日报和周报的自动化知识管理。
