# Dify 知识库集成功能使用说明

## 🎯 功能概述

已成功集成 Dify 知识库功能，支持将日报和周报自动上传到 Dify 知识库，与现有的 Discourse 论坛发送功能并行执行。

## ✅ 功能特点

### 1. 双重发送机制
- **Discourse 论坛**：继续发送到论坛（现有功能）
- **Dify 知识库**：同时上传到知识库（新增功能）
- **并行执行**：两个发送过程互不影响

### 2. 智能文档处理
- **临时文件管理**：自动创建和清理临时文件
- **高质量索引**：使用 Dify 的高质量索引技术
- **自定义分割**：支持自定义文档分割规则
- **层次化模型**：使用层次化文档模型

### 3. 完整配置支持
- **开关控制**：可独立启用/禁用 Dify 功能
- **参数配置**：支持数据集ID和API Token配置
- **错误处理**：完善的错误处理和日志记录

## 🔧 配置说明

### 基本配置

在 `schedule` 配置中添加以下 Dify 相关配置：

```json
{
  "schedule": {
    // ... 其他配置 ...
    
    "dify_enabled": true,
    "dify_dataset_id": "your_dataset_id_here",
    "dify_token": "your_dify_api_token_here"
  }
}
```

### 配置项详解

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `dify_enabled` | boolean | false | 是否启用 Dify 知识库上传 |
| `dify_dataset_id` | string | "" | Dify 知识库的数据集ID |
| `dify_token` | string | "" | Dify API 访问令牌 |

### 获取配置参数

#### 1. 获取数据集ID
1. 登录 Dify 控制台
2. 进入知识库管理
3. 选择或创建目标数据集
4. 在数据集详情页面找到数据集ID

#### 2. 获取API Token
1. 在 Dify 控制台进入 API 管理
2. 创建或查看现有的 API Key
3. 复制 API Token

## 📤 API 调用详情

### 请求信息
- **URL**: `https://api.dify.ai/v1/datasets/{dataset_id}/document/create-by-file`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Authorization**: Bearer {token}

### 请求参数

#### 文档处理规则
```json
{
  "indexing_technique": "high_quality",
  "process_rule": {
    "rules": {
      "pre_processing_rules": [
        {"id": "remove_extra_spaces", "enabled": true},
        {"id": "remove_urls_emails", "enabled": false}
      ],
      "segmentation": {
        "separator": "##",
        "max_tokens": 2048 
      }
    },
    "mode": "custom"
  },
  "doc_form": "hierarchical_model",
  "doc_type": "web_page"
}
```

#### 文件上传
- **文件格式**: UTF-8 编码的文本文件
- **文件名**: 基于日期和类型的标准命名
- **内容**: 完整的日报或周报内容

## 🔄 工作流程

### 日报流程（增强版）
1. **生成汇总**: 收集各群聊内容生成日报
2. **保存文件**: 保存到本地 `result` 目录
3. **发送 Discourse**: 发送到论坛（现有功能）
4. **上传 Dify**: 上传到知识库（新增功能）
   - 创建临时文件
   - 调用 Dify API
   - 清理临时文件

### 周报流程（增强版）
1. **生成周报**: 基于历史数据生成周报
2. **保存文件**: 保存到本地 `result` 目录
3. **发送 Discourse**: 发送到论坛（现有功能）
4. **上传 Dify**: 上传到知识库（新增功能）

### 文档命名规则

#### 日报文档
- **标题**: `YYYY-MM-DD_PKMer社群日报`
- **示例**: `2025-07-07_PKMer社群日报`

#### 周报文档
- **标题**: `PKMer社区周报_YYYY-MM-DD`
- **示例**: `PKMer社区周报_2025-07-07`

## 📋 使用示例

### 完整配置示例
参考 `example_dify_config.json` 文件获取完整的配置示例。

### 日志输出示例
```
[INFO] Scheduler: Uploading aggregate summary to Dify knowledge base...
[INFO] Successfully uploaded to Dify knowledge base: {"document_id": "xxx", "status": "success"}
[INFO] Scheduler: Successfully uploaded aggregate summary to Dify knowledge base.
```

## 🛠️ 故障排除

### 常见问题

#### 1. Dify 功能未执行
**检查项**:
- `dify_enabled` 是否为 true
- `dify_dataset_id` 是否已配置
- `dify_token` 是否已配置且有效

#### 2. 上传失败
**可能原因**:
- API Token 无效或过期
- 数据集ID 错误
- 网络连接问题
- Dify 服务异常

**解决方法**:
- 检查 API Token 是否正确
- 验证数据集ID 是否存在
- 检查网络连接
- 查看详细错误日志

#### 3. 文件格式问题
**检查项**:
- 确保内容为 UTF-8 编码
- 检查文件大小是否超限
- 验证内容格式是否正确

### 调试方法

#### 1. 查看日志
```
[INFO] Scheduler: Uploading aggregate summary to Dify knowledge base...
[ERROR] Failed to upload to Dify knowledge base. Status: 401, Error: Unauthorized
```

#### 2. 测试配置
运行 `python test_dify_integration.py` 进行功能测试。

#### 3. 手动验证
使用 curl 命令手动测试 API 调用：
```bash
curl --request POST \
  --url https://api.dify.ai/v1/datasets/{dataset_id}/document/create-by-file \
  --header 'Authorization: Bearer <token>' \
  --header 'Content-Type: multipart/form-data' \
  --form 'data={"indexing_technique":"high_quality","process_rule":{"mode":"custom", "rules": { "segmentation": {"separator":"##", "max_tokens":2048}}}}' \
  --form file=@test-file.txt
```

## 🔒 安全注意事项

1. **API Token 保护**: 确保 API Token 不被泄露
2. **权限控制**: 确保 Token 只有必要的权限
3. **数据隐私**: 注意上传内容的隐私性
4. **访问限制**: 了解 Dify API 的调用限制

## 📈 性能优化

1. **并行处理**: Discourse 和 Dify 上传并行执行
2. **错误隔离**: Dify 上传失败不影响其他功能
3. **资源管理**: 及时清理临时文件
4. **重试机制**: 可考虑添加失败重试逻辑

## 🎉 功能优势

1. **知识积累**: 自动构建知识库，便于检索和分析
2. **双重备份**: Discourse + Dify 双重存储保障
3. **智能索引**: 利用 Dify 的 AI 能力进行智能索引
4. **无缝集成**: 与现有流程完美集成，无需额外操作
5. **灵活配置**: 可独立控制是否启用 Dify 功能

---

**总结**: Dify 知识库集成功能已完全实现，提供与 Discourse 论坛并行的知识库上传能力，支持日报和周报的自动化知识管理，大幅提升内容的可检索性和价值。
