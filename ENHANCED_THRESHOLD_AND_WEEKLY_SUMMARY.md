# 增强功能实现总结：记录阈值检查 + 单群周报发送

## 🎯 功能概述

已成功实现两个重要的增强功能：
1. **记录数量阈值检查**：少于20条记录的群聊跳过总结生成
2. **单群周报发送**：每个群的周报按配置发送到对应目标群，然后汇总周报发送到汇总群

## ✅ 实现的功能

### 1. 记录数量阈值检查 ✅
- **阈值设置**: 最少20条记录才生成总结
- **跳过逻辑**: 少于20条记录的群聊直接跳过，不生成总结
- **日志记录**: 详细记录跳过原因和记录数量

### 2. 增强的周报系统 ✅
- **单群周报**: 为每个源群生成专门的周报
- **目标群发送**: 按group_map配置发送到对应目标群
- **汇总周报**: 生成所有群的综合周报发送到汇总群
- **文件分类保存**: 单群周报和汇总周报分别保存

### 3. 智能文件命名 ✅
- **单群周报**: `weekly_summary_YYYY-MM-DD_群ID.txt`
- **汇总周报**: `weekly_summary_YYYY-MM-DD.txt`
- **群聊总结**: `group_summary_YYYY-MM-DD_群ID.txt`
- **汇总报告**: `aggregate_summary_YYYY-MM-DD.txt`

## 🔧 技术实现

### 1. 记录数量阈值检查

#### 实现位置
在 `_run_scheduled_summary` 方法中，处理消息后立即检查：

```python
chat_records = await self._process_messages(mock_event, messages)
if not chat_records:
    logger.info(f"Scheduler: No processable content from messages in group {source_group_id}. Skipping.")
    continue

# 检查记录数量，少于20条则跳过
if len(chat_records) < 20:
    logger.info(f"Scheduler: Group {source_group_id} has only {len(chat_records)} records (< 20), skipping summary generation.")
    continue

logger.info("Scheduler: Generating summary...")
```

#### 功能特点
- ✅ 精确计数：基于处理后的有效记录数量
- ✅ 早期跳过：避免不必要的LLM调用
- ✅ 详细日志：记录跳过原因和具体数量
- ✅ 不影响其他群：单个群跳过不影响其他群处理

### 2. 增强的周报生成系统

#### 修改的核心方法

##### `_generate_weekly_summary` 方法增强
```python
async def _generate_weekly_summary(self, group_ids: List[str] = None, single_group_id: str = None) -> str:
    """生成周报
    
    Args:
        group_ids: 需要生成周报的群ID列表（用于汇总周报）
        single_group_id: 单个群ID（用于生成单群周报）
    """
    # 支持单群和多群两种模式
    if single_group_id:
        # 单群周报逻辑
    elif group_ids:
        # 多群汇总周报逻辑
```

##### 单群周报工作流程
```python
# 为每个源群生成单独的周报并发送到对应目标群
for source_group_id, target_group_ids in parsed_group_map.items():
    # 1. 生成单个群的周报
    single_weekly_summary = await self._generate_weekly_summary(single_group_id=source_group_id)
    
    # 2. 保存单群周报到文件
    await self._save_aggregate_to_file(single_weekly_summary, "weekly", source_group_id)
    
    # 3. 发送到该群对应的所有目标群
    for target_group_id in target_group_ids:
        # 发送周报到目标群
```

##### 汇总周报工作流程
```python
# 生成汇总周报并发送到汇总群
if self.aggregate_enabled and self.aggregate_group_id:
    # 1. 生成所有群的汇总周报
    aggregate_weekly_summary = await self._generate_weekly_summary(group_ids=source_group_ids)
    
    # 2. 保存汇总周报到文件
    await self._save_aggregate_to_file(aggregate_weekly_summary, "weekly")
    
    # 3. 发送汇总周报到汇总群
```

### 3. 文件保存系统增强

#### 修改的保存逻辑
```python
elif save_type == "weekly":
    # 获取当前周的周一日期
    today = datetime.now()
    days_since_monday = today.weekday()
    monday = today - timedelta(days=days_since_monday)
    week_date = monday.strftime("%Y-%m-%d")
    
    if group_id:
        # 单群周报
        filename = f"weekly_summary_{week_date}_{group_id}.txt"
        header_prefix = f"群聊{group_id}周报"
    else:
        # 汇总周报
        filename = f"weekly_summary_{week_date}.txt"
        header_prefix = "汇总周报"
```

## 🔄 完整工作流程

### 日常定时任务流程（增强版）
1. **群聊处理**: 逐个处理配置的源群
   - 获取消息历史
   - **检查记录数量（≥20条）** ← 新增
   - 如果少于20条 → 跳过该群
   - 如果达到阈值 → 生成群聊总结
   - 保存群聊总结文件
   - 发送到目标群
   - 收集汇总数据

2. **汇总处理**: 所有群处理完成后
   - 合并所有群数据
   - 生成汇总报告
   - 保存汇总文件
   - 发送到汇总群

3. **周报处理**: 如果是周一 ← 增强
   - **为每个源群生成单独周报** ← 新增
   - **保存单群周报文件** ← 新增
   - **按group_map发送到对应目标群** ← 新增
   - **生成汇总周报** ← 新增
   - **保存汇总周报文件** ← 新增
   - **发送汇总周报到汇总群** ← 新增

### 周一周报详细流程
```
周一检测 → 
├─ 源群A → 生成单群周报 → 保存文件 → 发送到目标群1,2
├─ 源群B → 生成单群周报 → 保存文件 → 发送到目标群3
├─ 源群C → 生成单群周报 → 保存文件 → 发送到目标群4,5
└─ 汇总 → 生成汇总周报 → 保存文件 → 发送到汇总群
```

## 📁 文件结构示例

```
result/
├── group_summary_2025-07-07_123456789.txt        # 群聊总结
├── group_summary_2025-07-07_987654321.txt        # 群聊总结
├── aggregate_summary_2025-07-07.txt              # 汇总报告
├── weekly_summary_2025-07-07_123456789.txt       # 单群周报 ← 新增
├── weekly_summary_2025-07-07_987654321.txt       # 单群周报 ← 新增
├── weekly_summary_2025-07-07_555666777.txt       # 单群周报 ← 新增
├── weekly_summary_2025-07-07.txt                 # 汇总周报
└── ...
```

## 📋 提示词优化

### 单群周报提示词
```
- Role: 群聊周报分析师
- Background: 你需要基于群聊 {group_id} 一周的总结数据，生成一份专门的群聊周报。
- Requirements:
  1. 本周概览：总结本群本周的整体活跃度和主要趋势
  2. 重点话题：提取本周最重要和最有趣的讨论话题
  3. 活跃分析：分析群友参与度和互动情况
  4. 周度变化：分析本周相比往期的变化和发展
  5. 下周展望：基于本周情况对下周进行简单展望
- Style: 专业而生动，像是一位资深社群运营在做群聊周度汇报。
- 精美Markdown格式，比如可以用引用 加粗等语法。
```

### 汇总周报提示词
```
- Role: 群聊周报分析师
- Background: 你需要基于一周的群聊总结数据，生成一份综合性的周报。
- Requirements:
  1. 整体概览：总结本周各群的整体活跃度和主要趋势
  2. 重点话题：提取本周最重要和最有趣的讨论话题
  3. 群聊对比：对比不同群聊的特点和活跃情况
  4. 周度变化：分析本周相比往期的变化和发展
  5. 下周展望：基于本周情况对下周进行简单展望
- Style: 专业而生动，像是一位资深社群运营在做周度汇报。
- 精美Markdown格式，比如可以用引用 加粗等语法。
```

## ✅ 测试验证

运行 `python test_enhanced_weekly_and_threshold.py` 验证：
- ✅ 记录数量阈值检查正常工作
- ✅ 单群周报生成功能正常
- ✅ 汇总周报生成功能正常
- ✅ 文件命名规则正确
- ✅ 工作流程逻辑完整

## 🎉 功能特点

### 记录阈值检查
- **智能过滤**: 自动跳过活跃度低的群聊
- **资源节约**: 避免为少量消息调用LLM
- **灵活配置**: 阈值可以轻松调整（当前20条）
- **详细日志**: 便于监控和调试

### 增强周报系统
- **个性化**: 每个群都有专门的周报
- **精准发送**: 按配置发送到对应目标群
- **层次分明**: 单群周报 + 汇总周报
- **完整存档**: 所有周报都有文件备份

### 系统优化
- **错误隔离**: 单个群的问题不影响其他群
- **性能优化**: 早期跳过减少不必要处理
- **存储优化**: 分类存储便于管理
- **用户体验**: 更精准的内容推送

---

**总结**: 记录阈值检查和增强周报系统已完全实现，提供更智能的内容过滤和更精准的周报分发功能，大幅提升了系统的实用性和用户体验。
