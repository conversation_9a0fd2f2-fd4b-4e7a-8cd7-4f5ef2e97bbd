# AstrBot Enhanced Chat Summary Plugin

[中文](README.md) | English

## Overview

The Enhanced Chat Summary plugin for AstrBot provides advanced functionality to automatically summarize and analyze chat conversations. It features improved code structure, multi-language support, and customizable summary formats.

## Features

- **Multi-language Support**: Interface available in English and Chinese
- **Multiple Summary Formats**: Export as Markdown, HTML, or plain text
- **Customizable Templates**: Use built-in templates or create your own
- **Keyword Highlighting**: Automatically highlight important terms
- **Configurable Summary Length**: Choose between short, medium, or long summaries
- **Timestamp and Participant Support**: Include conversation metadata when needed

## Installation

See [INSTALL.md](INSTALL.md) for detailed installation instructions or use the one-line installer:

```bash
./install.sh
```

## Usage

Please see the [User Guide](docs/USER_GUIDE.md) for detailed usage instructions.

## Configuration

The plugin can be configured through the AstrBot settings interface or by editing the configuration file directly.

Key configuration options include:

- Language selection
- Summary format and length
- Inclusion of timestamps and participant information
- Keyword highlighting settings
- Auto-save options

## Development

For those interested in contributing or modifying the plugin, see [Development Guide](docs/DEVELOPMENT.md).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- AstrBot developers for the plugin framework
- All contributors to the project

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for the project history and updates.