# 定时任务汇总功能使用说明

## 功能概述

定时任务汇总功能允许插件在执行定时总结任务时，自动收集所有群聊的原始消息，进行统一汇总，并发送到指定的汇总群。

## 工作流程

1. **定时触发**: 根据配置的 CRON 表达式定时执行
2. **群聊总结**: 对每个配置的源群进行总结并发送到对应目标群
3. **数据收集**: 如果启用汇总功能，收集所有群的原始消息数据
4. **汇总处理**: 将所有群的原始消息合并，使用汇总提示词生成综合汇总
5. **汇总发送**: 将汇总结果发送到指定的汇总群

## 配置说明

### 基本定时任务配置

```json
{
  "schedule": {
    "enabled": true,
    "cron": "0 22 * * *",
    "group_map": "{\"源群1\": [\"目标群1\"], \"源群2\": [\"目标群2\"]}",
    "summary_type": "daily",
    "as_image": false
  }
}
```

### 汇总功能配置

在 `schedule` 配置中添加以下汇总相关配置：

```json
{
  "schedule": {
    // ... 其他配置 ...
    
    "aggregate_enabled": true,
    "aggregate_prompt": "汇总提示词内容",
    "aggregate_group_id": "999888777"
  }
}
```

### 配置项详解

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `aggregate_enabled` | boolean | false | 是否启用汇总功能 |
| `aggregate_prompt` | string | "" | 汇总时使用的LLM提示词 |
| `aggregate_group_id` | string | "" | 接收汇总报告的群号 |

## 汇总提示词示例

```text
- Role: 多群聊汇总分析师
- Background: 你需要将来自多个群聊的总结内容进行汇总整理，形成一份综合性的日报。
- Requirements:
  1. 分群展示：按群聊分别展示各群的重要内容
  2. 重点突出：提取各群中最重要和有趣的内容
  3. 统一风格：保持整体风格一致，但体现各群特色
  4. 简洁明了：控制总体长度，突出关键信息
- OutputFormat: 按以下结构输出：
  1. 【今日群聊汇总】：简短概括所有群聊的整体情况
  2. 【各群动态】：分群展示重要内容
  3. 【今日总览】：对所有群聊的整体评价
```

## 完整配置示例

参考 `example_schedule_aggregate_config.json` 文件获取完整的配置示例。

## 使用步骤

### 1. 启用定时任务
```json
{
  "schedule": {
    "enabled": true,
    "cron": "0 22 * * *"
  }
}
```

### 2. 配置群聊映射
```json
{
  "group_map": "{\"825255377\": [\"874701466\"], \"111111111\": [\"222222222\"]}"
}
```

### 3. 启用汇总功能
```json
{
  "aggregate_enabled": true,
  "aggregate_group_id": "999888777"
}
```

### 4. 自定义汇总提示词（可选）
```json
{
  "aggregate_prompt": "你的自定义汇总提示词..."
}
```

## 执行逻辑

1. **定时触发**: 每天晚上10点（或自定义时间）自动执行
2. **逐群处理**: 
   - 获取源群消息历史
   - 生成群聊总结
   - 发送到对应目标群
   - 如果启用汇总，保存原始消息数据
3. **汇总处理**:
   - 合并所有群的原始消息
   - 为每个群添加标识头
   - 使用汇总提示词调用LLM
   - 生成综合汇总报告
4. **汇总发送**: 将汇总报告发送到指定汇总群

## 日志输出

汇总功能会输出详细的日志信息：

```
[INFO] Scheduler: Starting scheduled summary run...
[INFO] Scheduler: Processing source group 825255377 -> targets ['874701466']
[INFO] Scheduler: Fetching messages for group 825255377...
[INFO] Scheduler: Generating summary...
[INFO] Scheduler: Summary generated.
[INFO] Scheduler: Sending summary to UMO: aiocqhttp:GroupMessage:874701466
[INFO] Scheduler: Successfully sent scheduled summary to group 874701466.
[INFO] Scheduler: Starting aggregate summary generation...
[INFO] Scheduler: Sending aggregate summary to UMO: aiocqhttp:GroupMessage:999888777
[INFO] Scheduler: Successfully sent aggregate summary to group 999888777.
```

## 注意事项

1. **LLM提供商**: 确保配置了有效的LLM提供商ID
2. **群权限**: 确保机器人在所有相关群中有发送消息的权限
3. **消息量**: 汇总功能会处理所有群的消息，注意LLM的token限制
4. **错误处理**: 如果汇总过程出错，不会影响正常的群聊总结功能
5. **性能考虑**: 汇总功能会在所有群总结完成后执行，可能会增加总执行时间

## 故障排除

### 常见问题

1. **汇总功能未执行**
   - 检查 `aggregate_enabled` 是否为 true
   - 检查 `aggregate_group_id` 是否已设置

2. **汇总内容为空**
   - 检查是否有群聊产生了有效的消息数据
   - 检查LLM提供商是否正常工作

3. **汇总发送失败**
   - 检查机器人在汇总群中的权限
   - 检查群号是否正确

### 调试方法

1. 查看日志输出，确认汇总流程是否正常执行
2. 检查配置文件格式是否正确
3. 测试LLM提供商是否可用

## 更新日志

- v1.0.0: 初始版本，支持定时任务汇总功能
- 集成到现有的定时总结流程中
- 支持自定义汇总提示词
- 支持图片和文本两种输出格式
