{"prompt": {"description": "LLM提示词", "type": "string", "hint": "在此填写用于指导LLM总结聊天记录的提示词", "default": "- Role: 社群聊天记录总结分析师\n- Background: 你需要对群聊记录进行专业分析，提取关键信息并以美观、生动的格式呈现，帮助用户快速了解群聊精华内容。\n- Requirements:\n  1. 排版美观：使用「·」或「-」作为行前导符，适当使用空行分隔不同部分，保持层级清晰\n  2. 风格生动：避免僵硬的语气，使用生动活泼的表达方式描述群聊内容\n  3. 内容精炼：提取真正重要和有趣的内容，避免冗余\n  4. 符号使用：避免过度使用星号(*)和加粗标记，优先使用简洁的符号\n  5. 结构清晰：使用明确的分类和小标题，但不要过于机械化\n  6. 保持温度：用温暖自然的语气总结，仿佛是群里的一位细心观察者\n- OutputFormat: 按以下结构输出，但确保风格自然流畅：\n  1. 【今日速览】：简短概括群聊主要内容和氛围\n  2. 【热门话题】：按重要性排序的讨论话题，使用简洁的描述和关键要点\n  3. 【趣味时刻】：有趣的互动和金句，注重幽默和亮点\n  4. 【通知与提醒】：如果有任何重要通知或需要注意的事项\n  5. 【闲聊杂谈】：其他值得一提的小话题\n  6. 【群聊温度计】：对整体氛围的简短点评，语气轻松活泼\n- Style: 行文风格应当亲切自然，像是群里的老友在分享今日见闻，避免公式化和机械感。使用适当的表情符号增加活力，但不要过度。在涉及负面情绪或群员健康问题等敏感话题时，应当表达关心和支持，避免轻浮态度。"}, "llm_provider_id": {"type": "string", "default": "", "description": "LLM提供者ID，用于生成更新总结", "hint": "请填写有效的提供者ID"}, "max_records": {"description": "最大支持总结的记录数", "type": "int", "hint": "设置单次可获取的最大聊天记录数量，过大可能导致总结质量下降", "default": 300, "min": 10, "max": 500}, "extract_image_text": {"description": "是否提取图片内容", "type": "bool", "hint": "启用后将尝试识别图片中的文字内容（需服务端支持OCR）", "default": false}, "debug_mode": {"description": "调试模式设置", "type": "object", "items": {"enabled": {"description": "是否启用调试模式", "type": "bool", "default": false}, "log_level": {"description": "日志级别", "type": "string", "enum": ["info", "debug", "warning", "error"], "default": "info"}}}, "schedule": {"description": "定时总结设置", "type": "object", "items": {"enabled": {"description": "是否启用定时总结", "type": "bool", "default": false}, "cron": {"description": "CRON表达式，用于设置定时任务的执行周期", "type": "string", "default": "0 22 * * *"}, "source_group_id": {"description": "要总结的群号", "type": "string", "default": ""}, "target_group_id": {"description": "发送总结的目标群号（如果为空，则发送到源群）", "type": "string", "default": ""}, "group_map": {"type": "text", "default": "{\n\"825255377\": [\"874701466\", \"123456789\"], \n\"111111111\": [\"222222222\"]\n}", "description": "设置源群和目标群]", "editor_mode": true, "editor_language": "json", "editor_theme": "vs-dark"}, "summary_type": {"description": "总结模式count 或者 daily", "type": "string", "enum": ["count", "daily"], "default": "daily"}, "summary_count": {"description": "按数量总结时的消息条数", "type": "int", "default": 200}, "as_image": {"description": "是否生成图片", "type": "bool", "default": false}, "aggregate_enabled": {"description": "是否启用汇总功能", "type": "bool", "default": false}, "aggregate_prompt": {"description": "汇总提示词", "type": "text", "default": "- Role: 多群聊汇总分析师\n- Background: 你需要将来自多个群聊的总结内容进行汇总整理，形成一份综合性的日报。\n- Requirements:\n  1. 分群展示：按群聊分别展示各群的重要内容\n  2. 重点突出：提取各群中最重要和有趣的内容\n  3. 统一风格：保持整体风格一致\n  4. 简洁明了：控制总体长度，突出关键信息\n- OutputFormat: 按以下结构输出：\n  1. 【今日群聊汇总】：简短概括所有群聊的整体情况\n  2. 【各群动态】：分群展示重要内容\n  3. 【今日总览】：对所有群聊的整体评价", "editor_mode": true, "editor_language": "text", "editor_theme": "vs-dark"}, "aggregate_group_id": {"description": "汇总群ID", "type": "string", "default": "", "hint": "接收汇总报告的群号"}}}}