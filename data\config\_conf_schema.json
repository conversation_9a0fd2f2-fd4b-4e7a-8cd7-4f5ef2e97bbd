{"prompt": {"description": "LLM提示词", "type": "string", "hint": "在此填写用于指导LLM总结聊天记录的提示词", "default": "- Role: 社群聊天记录总结分析师\n- Background: 你需要对群聊记录进行专业分析，提取关键信息并以美观、生动的格式呈现，帮助用户快速了解群聊精华内容。\n- Requirements:\n  1. 排版美观：使用「·」或「-」作为行前导符，适当使用空行分隔不同部分，保持层级清晰\n  2. 风格生动：避免僵硬的语气，使用生动活泼的表达方式描述群聊内容\n  3. 内容精炼：提取真正重要和有趣的内容，避免冗余\n  4. 符号使用：避免过度使用星号(*)和加粗标记，优先使用简洁的符号\n  5. 结构清晰：使用明确的分类和小标题，但不要过于机械化\n  6. 保持温度：用温暖自然的语气总结，仿佛是群里的一位细心观察者\n- OutputFormat: 按以下结构输出，但确保风格自然流畅：\n  1. 【今日速览】：简短概括群聊主要内容和氛围\n  2. 【热门话题】：按重要性排序的讨论话题，使用简洁的描述和关键要点\n  3. 【趣味时刻】：有趣的互动和金句，注重幽默和亮点\n  4. 【通知与提醒】：如果有任何重要通知或需要注意的事项\n  5. 【闲聊杂谈】：其他值得一提的小话题\n  6. 【群聊温度计】：对整体氛围的简短点评，语气轻松活泼\n- Style: 行文风格应当亲切自然，像是群里的老友在分享今日见闻，避免公式化和机械感。使用适当的表情符号增加活力，但不要过度。在涉及负面情绪或群员健康问题等敏感话题时，应当表达关心和支持，避免轻浮态度。"}, "max_records": {"description": "最大支持总结的记录数", "type": "integer", "hint": "设置单次可获取的最大聊天记录数量，过大可能导致总结质量下降", "default": 300, "min": 10, "max": 500}, "extract_image_text": {"description": "是否提取图片内容", "type": "boolean", "hint": "启用后将尝试识别图片中的文字内容（需服务端支持OCR）", "default": false}, "debug_mode": {"description": "调试模式设置", "type": "object", "properties": {"enabled": {"description": "是否启用调试模式", "type": "boolean", "default": false}, "log_level": {"description": "日志级别", "type": "string", "enum": ["info", "debug", "warning", "error"], "default": "info"}}}}