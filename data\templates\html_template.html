<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 20px;
        }
        .meta {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        ul {
            padding-left: 20px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #ecf0f1;
            font-size: 0.8em;
            color: #95a5a6;
        }
        .highlight {
            background-color: #ffffcc;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    
    <div class="meta">
        <strong>Date:</strong> {{ date }}<br>
        <strong>Participants:</strong> {{ participants }}
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>{{ summary }}</p>
    </div>
    
    <div>
        <h2>Key Points</h2>
        <ul>
            {% for point in key_points %}
            <li>{{ point }}</li>
            {% endfor %}
        </ul>
    </div>
    
    <div>
        <h2>Action Items</h2>
        <ul>
            {% for item in action_items %}
            <li><input type="checkbox"> {{ item }}</li>
            {% endfor %}
        </ul>
    </div>
    
    <div>
        <h2>Questions Raised</h2>
        <ul>
            {% for question in questions %}
            <li>{{ question }}</li>
            {% endfor %}
        </ul>
    </div>
    
    <div class="footer">
        Generated by AstrBot Enhanced Chat Summary Plugin
    </div>
</body>
</html>