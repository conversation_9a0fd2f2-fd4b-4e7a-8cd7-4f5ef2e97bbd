# 平台兼容性指南

本文档详细说明 AstrBot 增强版聊天总结插件对各种平台的兼容性情况及使用特殊性。

## 支持的平台

本插件已经过测试并支持以下平台：

| 平台 | 支持状态 | 测试版本 | 特殊说明 |
|---|---|---|---|
| **QQ** | ✅ 完全支持 | V9.9.0+ | 支持群聊与私聊模式 |
| **微信** | ✅ 完全支持 | V8.0.35+ | 支持群聊与公众号交互 |
| **钉钉** | ✅ 完全支持 | V6.5.20+ | 支持群聊与工作群组 |
| **飞书** | ✅ 完全支持 | V6.6+ | 支持群聊与频道 |
| **企业微信** | ❓ 部分支持 | V3.1.12+ | 可能需要額外配置 |
| **TG** | ❓ 部分支持 | V8.0+ | 需要配置代理与 API 访问 |

## 平台特性适配

### QQ 平台特性

在 QQ 平台使用本插件时的特殊说明：

1. **命令触发方式**：
   - 在群聊中直接发送 `/消息总结 100` 即可触发
   - 管理员可使用 `/消息总结 100 debug` 查看调试信息

2. **消息格式支持**：
   - 支持文本、图片、表情、语音、文件等消息类型
   - 支持回复、引用和转发消息
   - ❗ QQ 中的图片存储类型与其他平台有差异，特别适配了该特性

3. **权限要求**：
   - 需要机器人或插件有群聊历史读取权限
   - 如果希望限制普通用户使用，请在配置中设置 `admin_only: true`

### 微信平台特性

在微信平台使用本插件时的特殊说明：

1. **命令触发方式**：
   - 在群聊中直接发送 `/消息总结 100` 即可触发
   - 支持自定义命令前缀，可在配置中修改 `command_trigger` 项

2. **消息格式限制**：
   - ❗ 微信群聊返回消息长度有限制，插件默认开启了智能截断
   - 微信对 Markdown 格式支持有限，推荐使用纯文本模式（在配置中设置 `output.format: "text"`）
   - 大型群聊处理可能需要更长的响应时间，建议适当增加 `request_timeout` 参数

3. **权限与限制**：
   - 微信聊天记录获取应符合微信官方开放接口指南
   - 对于非群主或非管理员账号，微信可能有更嚴格的限制

### 钉钉平台特性

在钉钉平台使用本插件时的特殊说明：

1. **命令增强**：
   - 钉钉支持更丰富的命令扩展，除了基本命令外，还支持：
     - `/总结@用户` 仅总结特定用户的发言
     - `/每日总结` 自动总结每天的聊天记录
     - `/设置总结` 启动参数设置面板

2. **消息格式支持**：
   - 钉钉对Markdown格式支持极佳，推荐使用 `output.format: "markdown"` 设置
   - 支持复杂文档格式的抽取与总结，包括 Office 文档和 PDF

3. **云端集成**：
   - 可与钉钉文档、空间等云应用集成
   - 允许将总结结果直接保存为钉钉文档

### 飞书平台特性

在飞书平台使用本插件时的特殊说明：

1. **消息类型支持**：
   - 支持较完整的消息类型，包括文档、表格、投票等
   - 支持小程序消息结构的提取和处理

2. **频道与群组协同**：
   - 可在频道和群组中跟踪讨论进展
   - 支持多主题消息的追踪和分类

3. **高级显示支持**：
   - 飞书支持丰富的卡片交互式显示
   - 可使用互动卡片展示总结结果

### 企业微信平台特性

在企业微信平台使用本插件时的特殊说明：

1. **配置需求**：
   - 需要配置特定的企业微信访问凭证
   - 在 `config.json` 中需添加 `platform_specific.wecom` 配置段

2. **限制因素**：
   - 企业微信客户端对非正式应用可能有严格审核
   - 访问频率和消息长度有额外限制

### TG 平台特性

在 Telegram 平台使用本插件时的特殊说明：

1. **配置要求**：
   - 需要配置 Telegram Bot API 访问令牌
   - 部分特殊地区可能需要代理访问支持

2. **命令触发兼容性**：
   - 支持使用 `/summary 100` 等英文命令
   - 可配置多种命令别名

## 多平台转换配置

不同平台之间迁移时，可能需要调整的配置项：

1. **格式化输出调整**：
   - 微信和QQ：建议使用纯文本格式
   - 钉钉和飞书：可使用完整 Markdown 格式
   - Telegram：支持 HTML 和 Markdown 格式

2. **消息长度调整**：
   - 微信/QQ: `max_length: 2000`
   - 钉钉/飞书: `max_length: 5000`
   - Telegram: `max_length: 4000`

## 平台通用兼容性提升

为确保在任何平台上均能良好运行，建议采取以下措施：

1. **使用通用配置文件**：
   - 在 `data/config/` 下创建平台特定的配置文件，如 `config.qq.json`、`config.weixin.json` 等
   - 启动时自动检测平台并加载对应配置

2. **异常处理增强**：
   - 启用 `retry_on_error: true` 自动重试机制
   - 使用 `fallback_format: "text"` 在格式化失败时适配纯文本

3. **平台检测自适应**：
   - 启用 `auto_platform_detection: true` 自动检测并适配当前平台
   - 配置 `platform_specific` 块中定义平台专属设置

## 问题排查与平台适配

通过增加调试信息确定平台兼容性问题：

1. **开启调试模式**：
   ```
   /消息总结 10 debug
   ```

2. **查看平台识别信息**：
   ```
   /消息总结 platform
   ```

3. **检测诸如消息长度限制等平台参数**：
   ```
   /消息总结 limits
   ```

## 平台兼容性更新计划

我们计划对以下平台提供更完善的支持：

1. **即将支持**：
   - 安卓手机微信口袋版
   - 新版企业微信

2. **计划支持**：
   - Discord
   - Slack

3. **正在调研**：
   - Matrix
   - WhatsApp

如遇到平台兼容性问题，请在 GitHub 仓库提交 Issue，并标记 `platform-compatibility` 标签。