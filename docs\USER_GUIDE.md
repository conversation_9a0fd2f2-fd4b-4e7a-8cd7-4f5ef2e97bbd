# AstrBot 增强版聊天总结插件 - 用户指南

这份指南将帮助您全面了解如何使用增强版聊天总结插件。

## 目录

- [功能概述](#功能概述)
- [安装方法](#安装方法)
- [使用方法](#使用方法)
- [配置选项](#配置选项)
- [常见问题](#常见问题)
- [使用案例](#使用案例)

## 功能概述

AstrBot 增强版聊天总结插件是一个基于 LLM 的智能历史聊天记录总结工具，支持多种消息类型和自定义输出格式。主要功能包括：

- 全面的消息类型支持：文本、表情、图片、文件、语音、@消息等
- 结构化输出：分类清晰的总结，包括「今日速览」、「热门话题」等板块
- 高度可定制：自定义提示词和输出模板
- 稳定可靠：完善的错误处理机制
- 调试模式：提供详细的日志和调试信息

## 安装方法

请参考 [INSTALL.md](../INSTALL.md) 文件获取详细的安装指南。

## 使用方法

### 基本命令

```
/消息总结 100         # 总结最近100条消息
/消息总结 50 debug    # 调试模式(仅管理员)
```

### 命令参数说明

- **消息数量**：必填参数，指定要总结的消息数量，范围为 1-300
- **debug**：可选参数，开启调试模式，仅管理员可用

### 使用场景

1. **群聊摘要**：快速了解群聊的关键信息和热点话题
2. **会议纪要**：总结会议内容和决策要点
3. **信息跟踪**：跟踪群组内的重要信息和通知
4. **休假回顾**：快速了解休假期间错过的群聊内容

## 配置选项

插件支持以下配置项目：

| 配置项 | 描述 | 默认值 | 可选值 |
|--------|------|--------|--------|
| prompt | LLM提示词，控制总结风格 | 详见config_schema.json | 自定义文本 |
| max_records | 最大支持总结的记录数 | 300 | 10-500 |
| extract_image_text | 是否提取图片内容 | false | true/false |
| debug_mode | 调试模式设置 | 默认关闭 | 见下表 |

debug_mode配置项：

| 子配置项 | 描述 | 默认值 |
|--------|------|--------|
| enabled | 是否启用调试模式 | false |
| log_level | 日志级别 | "info" |

### 配置文件位置

配置文件位于 AstrBot 目录下的 `data/config/astrbot_plugin_chatsummary_config.json`。

### 配置示例

```json
{
  "prompt": "- Role: 群聊分析师\n- Background: 你需要分析群聊记录，提取关键信息\n- OutputFormat: \n  1. 【今日速览】\n  2. 【热门话题】\n  3. 【趣味时刻】",
  "max_records": 300,
  "extract_image_text": false,
  "debug_mode": {
    "enabled": false,
    "log_level": "info"
  }
}
```

## 常见问题

### 问题：无法通过pip安装

**解决方案**：
- 目前插件尚未发布到PyPI，请使用源码安装方法
- 确保使用的是最新版本的AstrBot(>=0.1.0)

### 问题：总结结果不理想

**解决方案**：
- 自定义提示词，调整输出格式
- 减少总结的消息数量，提高质量
- 确保群聊内容有足够的信息量

### 问题：总结生成速度较慢

**解决方案**：
- 减少总结消息数量
- 使用更快的LLM模型
- 关闭`extract_image_text`功能

## 使用案例

### 案例1：每日群聊摘要

每天工作结束前，使用命令总结当天的群聊内容：

```
/消息总结 200
```

### 案例2：项目讨论会议记录

在项目讨论结束后，总结讨论内容和决策：

```
/消息总结 100
```

### 案例3：管理员调试模式

当总结功能出现问题时，管理员可以使用调试模式追踪问题：

```
/消息总结 50 debug
```

## 更多帮助

如果您有更多问题或需要更详细的帮助，请参考：

- [项目 GitHub 仓库](https://github.com/jokeryuyc/astrbot-enhanced-chatsummary)
- [原始项目](https://github.com/laopanmemz/astrbot_plugin_chatsummary)
- [常见问题](https://github.com/jokeryuyc/astrbot-enhanced-chatsummary/issues)

欢迎提交问题和建议！