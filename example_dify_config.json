{"prompt": "- Role: 社群聊天记录总结分析师\n- Background: 你需要对群聊记录进行专业分析，提取关键信息并以美观、生动的格式呈现，帮助用户快速了解群聊精华内容。\n- Requirements:\n  1. 排版美观：使用「·」或「-」作为行前导符，适当使用空行分隔不同部分，保持层级清晰\n  2. 风格生动：避免僵硬的语气，使用生动活泼的表达方式描述群聊内容\n  3. 内容精炼：提取真正重要和有趣的内容，避免冗余\n  4. 符号使用：避免过度使用星号(*)和加粗标记，优先使用简洁的符号\n  5. 结构清晰：使用明确的分类和小标题，但不要过于机械化\n  6. 保持温度：用温暖自然的语气总结，仿佛是群里的一位细心观察者\n- OutputFormat: 按以下结构输出，但确保风格自然流畅：\n  1. 【今日速览】：简短概括群聊主要内容和氛围\n  2. 【热门话题】：按重要性排序的讨论话题，使用简洁的描述和关键要点\n  3. 【趣味时刻】：有趣的互动和金句，注重幽默和亮点\n  4. 【通知与提醒】：如果有任何重要通知或需要注意的事项\n  5. 【闲聊杂谈】：其他值得一提的小话题\n  6. 【群聊温度计】：对整体氛围的简短点评，语气轻松活泼\n- Style: 行文风格应当亲切自然，像是群里的老友在分享今日见闻，避免公式化和机械感。使用适当的表情符号增加活力，但不要过度。在涉及负面情绪或群员健康问题等敏感话题时，应当表达关心和支持，避免轻浮态度。", "llm_provider_id": "your_llm_provider_id", "max_records": 300, "extract_image_text": false, "debug_mode": {"enabled": false, "log_level": "info"}, "schedule": {"enabled": true, "cron": "0 22 * * *", "group_map": "{\n\"825255377\": [\"874701466\"], \n\"111111111\": [\"222222222\"],\n\"333333333\": [\"444444444\"]\n}", "summary_type": "daily", "summary_count": 200, "as_image": false, "_comment_aggregate": "=== 汇总功能配置 ===", "aggregate_enabled": true, "aggregate_prompt": "- Role: 多群聊汇总分析师\n- Background: 你需要将来自多个群聊的总结内容进行汇总整理，形成一份综合性的日报。\n- Requirements:\n  1. 分群展示：按群聊分别展示各群的重要内容\n  2. 重点突出：提取各群中最重要和有趣的内容\n  3. 统一风格：保持整体风格一致，但体现各群特色\n  4. 简洁明了：控制总体长度，突出关键信息\n  5. 时间感知：体现时间顺序和关联性\n- OutputFormat: 按以下结构输出：\n  1. 【今日群聊汇总】：简短概括所有群聊的整体情况和主要趋势\n  2. 【各群动态】：\n     · 群聊A：重要内容和亮点\n     · 群聊B：重要内容和亮点\n     · 群聊C：重要内容和亮点\n  3. 【跨群话题】：如果有相同话题在多个群中讨论\n  4. 【今日总览】：对所有群聊的整体评价和明日展望\n- Style: 保持专业而亲切的语调，像是一位资深的社群观察员在做日报汇总。\n- 精美Markdown格式，比如可以用引用 加粗等语法。", "aggregate_group_id": "999888777", "_comment_dify": "=== Dify 知识库配置 ===", "dify_enabled": true, "dify_dataset_id": "your_dataset_id_here", "dify_token": "your_dify_api_token_here"}}