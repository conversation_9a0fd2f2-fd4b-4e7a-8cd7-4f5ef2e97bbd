"""
聊天记录总结插件 - 增强版
提供智能的聊天记录总结功能，支持多平台和多语言
"""

import os
import json
from datetime import datetime, time as dt_time,timedelta
import logging
from typing import List, Dict, Any, Optional, Union, Type
import time
import markdown
import requests
import re
# 尝试导入AstrBot和调度器依赖
try:
    from astrbot.api.event import filter, AstrMessageEvent, MessageChain
    from astrbot.api.star import Context, Star, register
    import astrbot.api.message_components as Comp
    from astrbot.api import logger
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    from astrbot.api.star import StarTools

    ASTRBOT_AVAILABLE = True
except ImportError:
    ASTRBOT_AVAILABLE = False
    # 定义模拟的函数和类
    class MockContext:
        """模拟的Context类，用于测试环境"""
        def get_using_provider(self):
            return MockProvider()

    class MockProvider:
        """模拟的Provider类，用于测试环境"""
        async def text_chat(self, input, max_tokens=None, temperature=None):
            class MockResponse:
                completion_text = "模拟的总结结果 - 测试环境"
            return MockResponse()

    class MockStar:
        """模拟的Star类，用于测试环境"""
        def __init__(self, context=None):
            self.context = context or MockContext()

    Context = MockContext
    Star = MockStar
    filter = type('MockFilter', (), {'command': lambda x: lambda y: y})
    register = lambda *args, **kwargs: lambda cls: cls
    CronTrigger = None
    AsyncIOScheduler = None


# 导入国际化支持
import sys
# 添加当前目录到Python路径，确保可以导入本地的i18n模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from i18n import I18n

# 设置日志
#logger = logging.getLogger("astrbot.plugin.chatsummary")

# 插件类定义
@register("astrbot_enhanced_chatsummary", "jokeryuyc",
         "增强版聊天记录总结插件，支持多语言和更多功能", "1.0.3",
         "https://github.com/jokeryuyc/astrbot-enhanced-chatsummary")
class EnhancedChatSummary(MockStar if not ASTRBOT_AVAILABLE else Star):
    """聊天记录总结插件主类，提供智能的聊天记录总结功能"""

    def __init__(self, context: Any, config: Dict[str, Any] = None):
        """初始化插件实例

        Args:
            context: AstrBot上下文
            config: 插件配置
        """
        if ASTRBOT_AVAILABLE:
            super().__init__(context)
        else:
            super().__init__(context)

        self.context = context
        self.config = config or {}

        # 初始化i18n
        self.i18n = I18n(self.config.get("language", "zh_CN"))

        # 获取配置项
        self.max_records = self.config.get("max_records", 300)
        self.extract_image_text = self.config.get("extract_image_text", False)
        self.debug_mode = self.config.get("debug_mode", {}).get("enabled", False)
        self.llm_provider_id=self.config.get("llm_provider_id", "")
        self.schedule_config = self.config.get("schedule", {})

        # 获取提示词
        self.prompt = self.config.get("prompt", "- Role: 社群聊天记录总结分析师- Background: 你需要对群聊记录进行专业分析，不仅要总结，还要对关键的话题和争论进行复盘。最后提取关键信息并以美观、生动的格式呈现，帮助用户快速了解群聊精华内容。- Requirements:  1. 排版美观：使用「·」或「-」作为行前导符，适当使用空行分隔不同部分，保持层级清晰  2. 风格生动：避免僵硬的语气，使用生动活泼的表达方式描述群聊内容  3. 内容精炼：提取真正重要和有趣的内容，避免冗余  ，确保总结内容要客观真实，避免语言过于精炼，引发歧义。4. 符号使用：避免过度使用星号(*)和加粗标记，优先使用简洁的符号  5. 结构清晰：使用明确的分类和小标题，但不要过于机械化  6. 保持温度：用温暖自然的语气总结，仿佛是群里的一位细心观察者- OutputFormat: 按以下结构输出，但确保风格自然流畅")
        # 汇总配置
        self.aggregate_enabled = self.schedule_config.get("aggregate_enabled", False)
        self.aggregate_prompt = self.schedule_config.get("aggregate_prompt", "")
        self.aggregate_group_id = self.schedule_config.get("aggregate_group_id", "")

        # 配置文件路径
        self.config_path = os.path.join('data', 'config', 'config.json')
        self.admin_config_path = os.path.join('data', 'config', 'admin_config.json')

        # 使用 print 而不是 logger 来避免日志格式化问题
        logger.info(f"EnhancedChatSummary plugin initialized with max_records={self.max_records}")

        # 初始化并启动调度器
        if ASTRBOT_AVAILABLE and self.schedule_config.get("enabled", False):
            self.scheduler = AsyncIOScheduler(timezone="Asia/Shanghai")
            cron_str = self.schedule_config.get("cron", "0 22 * * *")

            try:
                self.scheduler.add_job(
                    self._run_scheduled_summary,
                    trigger=CronTrigger.from_crontab(cron_str, timezone="Asia/Shanghai"),
                    id="enhanced_chat_summary_job",  # <--- 添加一个唯一的任务ID
                    replace_existing=True          # <--- 关键！防止插件重载时重复添加任务
                )
                self.scheduler.start()
                logger.info(f"Scheduled summary job added with cron: {cron_str}")
            except Exception as e:
                logger.error(f"Failed to add scheduled job: {e}")

    async def terminate(self):
        """插件终止时关闭调度器"""
        if hasattr(self, 'scheduler') and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Scheduler shut down.")
    async def _text_to_image(self,content):
        html_content = markdown.markdown(content, extensions=[
                            'fenced_code', 'tables', 'codehilite', 'sane_lists'
                        ])
        tmpl = """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

                        :root {
                            --primary-color: #4A6FFF;
                            --primary-light: #EEF2FF;
                            --accent-color: #FF6B6B;
                            --bg-color: #FFFFFF;
                            --text-color: #333344;
                            --secondary-text: #666677;
                            --border-color: #E5E8FF;
                            --code-bg: #2E3440;
                            --code-color: #ECEFF4;
                            --shadow-sm: 0 4px 12px rgba(74, 111, 255, 0.08); /* Adjusted shadow for larger scale */
                            --shadow-md: 0 8px 24px rgba(74, 111, 255, 0.12); /* Adjusted shadow for larger scale */
                        }

                        * {
                            box-sizing: border-box;
                            margin: 0;
                            padding: 0;
                        }

                        body {
                            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
                            font-size: 26px; /* Increased for mobile readability */
                            line-height: 1.6; /* Slightly adjusted line height for better density */
                            color: var(--text-color);
                            background: var(--bg-color);
                            width: 1080px; /* Target width for the content */
                            overflow-x: hidden;
                        }

                        .container {
                            width: 100%;
                            background: white;
                            box-shadow: var(--shadow-md); /* Added a subtle shadow to the whole container */
                            border-radius: 16px; /* Added overall roundness */
                            overflow: hidden; /* Ensure content is clipped by border-radius */
                        }

                        .header {
                            background: var(--primary-color);
                            padding: 40px 60px; /* Increased padding */
                            color: white;
                            position: relative;
                            overflow: hidden;
                            border-top-left-radius: 16px; /* Match container radius */
                            border-top-right-radius: 16px; /* Match container radius */
                        }

                        .header::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 180px; /* Increased size */
                            height: 180px; /* Increased size */
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            transform: translate(30%, -30%);
                        }

                        .header::before {
                            content: '';
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            width: 120px; /* Increased size */
                            height: 120px; /* Increased size */
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            transform: translate(-30%, 30%);
                        }

                        .content {
                            padding: 40px 60px; /* Increased padding */
                        }

                        /* 标题样式 */
                        h1, h2, h3, h4, h5, h6 {
                            font-weight: 700;
                            line-height: 1.3;
                            margin-top: 1.4em; /* Relative to current font-size */
                            margin-bottom: 0.7em; /* Relative to current font-size */
                            color: var(--primary-color);
                        }

                        h1 {
                            font-size: 38px; /* Increased */
                            margin-top: 0.7em;
                            padding-bottom: 15px; /* Increased */
                            border-bottom: 3px solid var(--primary-light); /* Thicker border */
                        }

                        h2 {
                            font-size: 32px; /* Increased */
                            padding-left: 20px; /* Increased */
                            border-left: 6px solid var(--primary-color); /* Thicker border */
                        }

                        h3 {
                            font-size: 28px; /* Increased */
                            color: var(--accent-color);
                        }

                        /* 段落样式 */
                        p {
                            margin: 0 0 26px; /* Increased margin */
                            word-break: break-word;
                        }

                        /* 表格样式 */
                        table {
                            width: 100%;
                            margin: 30px 0; /* Increased margin */
                            border-collapse: collapse;
                            border-radius: 12px; /* Increased radius */
                            overflow: hidden;
                            box-shadow: var(--shadow-sm);
                            font-size: 22px; /* Increased */
                        }

                        th, td {
                            padding: 18px 25px; /* Increased padding */
                            border: 1px solid var(--border-color);
                        }

                        th {
                            background-color: var(--primary-light);
                            color: var(--primary-color);
                            font-weight: 500;
                            text-align: left;
                        }

                        tr:nth-child(even) {
                            background-color: rgba(238, 242, 255, 0.5);
                        }

                        /* 代码块样式 */
                        pre {
                            background-color: var(--code-bg);
                            color: var(--code-color);
                            padding: 25px 30px; /* Increased padding */
                            border-radius: 15px; /* Increased radius */
                            font-family: 'JetBrains Mono', 'Fira Code', 'Menlo', 'Monaco', monospace;
                            font-size: 20px; /* Increased */
                            overflow-x: auto;
                            margin: 30px 0; /* Increased margin */
                            box-shadow: var(--shadow-sm);
                            position: relative;
                        }

                        pre::before {
                            content: '';
                            position: absolute;
                            top: 20px; /* Increased */
                            left: 20px; /* Increased */
                            width: 15px; /* Increased */
                            height: 15px; /* Increased */
                            background: #FF5F56;
                            border-radius: 50%;
                            box-shadow: 25px 0 0 #FFBD2E, 50px 0 0 #27C93F; /* Increased spacing */
                        }

                        code {
                            font-family: 'JetBrains Mono', 'Fira Code', 'Menlo', 'Monaco', monospace;
                            background: var(--primary-light);
                            color: var(--primary-color);
                            padding: 4px 8px; /* Slightly increased padding */
                            border-radius: 6px; /* Slightly increased radius */
                            font-size: 0.9em; /* Relative to body font size */
                        }

                        pre code {
                            background: transparent;
                            color: inherit;
                            padding: 0;
                            border-radius: 0;
                            padding-top: 15px; /* More space below dots */
                            display: block;
                        }

                        /* 图片样式 */
                        img {
                            max-width: 100%;
                            height: auto;
                            border-radius: 15px; /* Increased radius */
                            display: block;
                            margin: 30px auto; /* Increased margin */
                            box-shadow: var(--shadow-md);
                        }

                        /* 列表样式 */
                        ul, ol {
                            margin: 24px 0; /* Increased margin */
                            padding-left: 35px; /* Increased padding */
                        }

                        li {
                            margin-bottom: 12px; /* Increased margin */
                            position: relative;
                        }

                        ul li::marker {
                            color: var(--primary-color);
                        }

                        /* 链接样式 */
                        a {
                            color: var(--primary-color);
                            text-decoration: none;
                            font-weight: 500;
                            border-bottom: 1px solid transparent;
                            transition: border-color 0.2s;
                        }

                        a:hover {
                            border-bottom-color: var(--primary-color);
                        }

                        /* 引用样式 */
                        blockquote {
                            border-left: 6px solid var(--accent-color); /* Thicker border */
                            color: var(--secondary-text);
                            padding: 20px 25px; /* Increased padding */
                            margin: 28px 0; /* Increased margin */
                            background: #FFF8F8;
                            border-radius: 0 12px 12px 0; /* Increased radius */
                            font-style: italic;
                        }

                        blockquote p:last-child {
                            margin-bottom: 0;
                        }

                        /* 分割线 */
                        hr {
                            border: 0;
                            height: 3px; /* Thicker line */
                            background: linear-gradient(to right, var(--border-color), var(--primary-light), var(--border-color));
                            margin: 40px 0; /* Increased margin */
                        }

                        /* 底部装饰 */
                        .footer {
                            text-align: center;
                            padding: 25px; /* Increased padding */
                            color: var(--secondary-text);
                            font-size: 20px; /* Increased */
                            background: var(--primary-light);
                            border-top: 1px solid var(--border-color);
                            border-bottom-left-radius: 16px; /* Match container radius */
                            border-bottom-right-radius: 16px; /* Match container radius */
                        }

                        /* 代码高亮 - These styles are generally fine as they are colors */
                        .codehilite .hll { background-color: #49483e }
                        .codehilite .c { color: #75715e } /* Comment */
                        .codehilite .err { color: #f92672 } /* Error */
                        .codehilite .k { color: #66d9ef } /* Keyword */
                        .codehilite .l { color: #ae81ff } /* Literal */
                        .codehilite .n { color: #f8f8f2 } /* Name */
                        .codehilite .o { color: #f92672 } /* Operator */
                        .codehilite .p { color: #f8f8f2 } /* Punctuation */
                        .codehilite .ch { color: #75715e } /* Comment.Hashbang */
                        .codehilite .cm { color: #75715e } /* Comment.Multiline */
                        .codehilite .cp { color: #75715e } /* Comment.Preproc */
                        .codehilite .c1 { color: #75715e } /* Comment.Single */
                        .codehilite .cs { color: #75715e } /* Comment.Special */
                        .codehilite .gd { color: #f92672 } /* Generic.Deleted */
                        .codehilite .ge { font-style: italic } /* Generic.Emph */
                        .codehilite .gi { color: #a6e22e } /* Generic.Inserted */
                        .codehilite .gs { font-weight: bold } /* Generic.Strong */
                        .codehilite .gu { color: #75715e } /* Generic.Subheading */
                        .codehilite .kc { color: #66d9ef } /* Keyword.Constant */
                        .codehilite .kd { color: #66d9ef } /* Keyword.Declaration */
                        .codehilite .kn { color: #f92672 } /* Keyword.Namespace */
                        .codehilite .kp { color: #66d9ef } /* Keyword.Pseudo */
                        .codehilite .kr { color: #66d9ef } /* Keyword.Reserved */
                        .codehilite .kt { color: #66d9ef } /* Keyword.Type */
                        .codehilite .ld { color: #e6db74 } /* Literal.Date */
                        .codehilite .m { color: #ae81ff } /* Literal.Number */
                        .codehilite .s { color: #e6db74 } /* Literal.String */
                        .codehilite .na { color: #a6e22e } /* Name.Attribute */
                        .codehilite .nb { color: #f8f8f2 } /* Name.Builtin */
                        .codehilite .nc { color: #a6e22e } /* Name.Class */
                        .codehilite .no { color: #66d9ef } /* Name.Constant */
                        .codehilite .nd { color: #a6e22e } /* Name.Decorator */
                        .codehilite .ni { color: #f8f8f2 } /* Name.Entity */
                        .codehilite .ne { color: #a6e22e } /* Name.Exception */
                        .codehilite .nf { color: #a6e22e } /* Name.Function */
                        .codehilite .nl { color: #f8f8f2 } /* Name.Label */
                        .codehilite .nn { color: #f8f8f2 } /* Name.Namespace */
                        .codehilite .nx { color: #a6e22e } /* Name.Other */
                        .codehilite .py { color: #f8f8f2 } /* Name.Property */
                        .codehilite .nt { color: #f92672 } /* Name.Tag */
                        .codehilite .nv { color: #f8f8f2 } /* Name.Variable */
                        .codehilite .ow { color: #f92672 } /* Operator.Word */
                        .codehilite .w { color: #f8f8f2 } /* Text.Whitespace */
                        .codehilite .mb { color: #ae81ff } /* Literal.Number.Bin */
                        .codehilite .mf { color: #ae81ff } /* Literal.Number.Float */
                        .codehilite .mh { color: #ae81ff } /* Literal.Number.Hex */
                        .codehilite .mi { color: #ae81ff } /* Literal.Number.Integer */
                        .codehilite .mo { color: #ae81ff } /* Literal.Number.Oct */
                        .codehilite .sa { color: #e6db74 } /* Literal.String.Affix */
                        .codehilite .sb { color: #e6db74 } /* Literal.String.Backtick */
                        .codehilite .sc { color: #e6db74 } /* Literal.String.Char */
                        .codehilite .dl { color: #e6db74 } /* Literal.String.Delimiter */
                        .codehilite .sd { color: #e6db74 } /* Literal.String.Doc */
                        .codehilite .s2 { color: #e6db74 } /* Literal.String.Double */
                        .codehilite .se { color: #ae81ff } /* Literal.String.Escape */
                        .codehilite .sh { color: #e6db74 } /* Literal.String.Heredoc */
                        .codehilite .si { color: #e6db74 } /* Literal.String.Interpol */
                        .codehilite .sx { color: #e6db74 } /* Literal.String.Other */
                        .codehilite .sr { color: #e6db74 } /* Literal.String.Regex */
                        .codehilite .s1 { color: #e6db74 } /* Literal.String.Single */
                        .codehilite .ss { color: #e6db74 } /* Literal.String.Symbol */
                        .codehilite .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */
                        .codehilite .fm { color: #a6e22e } /* Name.Function.Magic */
                        .codehilite .vc { color: #f8f8f2 } /* Name.Variable.Class */
                        .codehilite .vg { color: #f8f8f2 } /* Name.Variable.Global */
                        .codehilite .vi { color: #f8f8f2 } /* Name.Variable.Instance */
                        .codehilite .vm { color: #f8f8f2 } /* Name.Variable.Magic */
                        .codehilite .il { color: #ae81ff } /* Literal.Number.Integer.Long */
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1 style="color: white; margin: 0; border: none; font-size: 32px;">群聊太长不看版</h1> <!-- Adjusted header h1 font size -->
                        </div>
                        <div class="content">
                            {{ html_content }}
                        </div>
                        <div class="footer">
                            由 PKMer AI 助手生成 • 仅供参考 • {{date_str}}
                        </div>
                    </div>
                </body>
                </html>
            """
        render_options = {
            "type": "jpeg",
            "quality": 80,
            "full_page": True,
            "clip": {
                "x": 0,
                "y": 0,
                "width": 1080,
                "height": 99999
            },
            "scale": "device",
            "animations": "disabled",
            "caret": "hide"
            }
        date_str = datetime.now().strftime("%Y-%m-%d")
        render_data = {"html_content": html_content,"date_str":date_str}
        url = await self.html_render(tmpl, render_data, return_url=True, options=render_options)
        return url
    def safe_parse_json(self,json_str):
        """
        安全解析JSON字符串
        :param json_str: JSON格式的字符串
        :return: 解析后的字典，解析失败返回空字典
        """
        try:
            # 去除首尾空白字符（如果有）
            cleaned_str = json_str.strip()
            # 解析为Python对象
            parsed = json.loads(cleaned_str)

            # 类型检查确保是字典
            if not isinstance(parsed, dict):
                raise ValueError("Parsed JSON is not a dictionary")

            # 验证数据结构格式
            result = {}
            for key, value in parsed.items():
                # 确保键是字符串或数字
                if not isinstance(key, (str, int)):
                    raise ValueError(f"Invalid key type: {type(key)}")

                # 确保值是列表
                if not isinstance(value, list):
                    raise ValueError(f"Value should be list, got {type(value)}")

                # 统一转换为字符串格式
                result[str(key)] = [str(item) for item in value]

            return result

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {}
        except ValueError as e:
            logger.error(f"数据格式错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return {}
    async def _run_scheduled_summary(self):
        logger.info("Scheduler: Starting scheduled summary run...")
        try:
            schedule = self.schedule_config
            group_map = schedule.get("group_map")
            as_image = schedule.get("as_image", False)
            summary_type = schedule.get("summary_type", "daily")
            summary_count = schedule.get("summary_count", 200)
            cron_str = schedule.get("cron", "0 22 * * *")
            parsed_group_map = self.safe_parse_json(group_map)
            # 兼容老配置
            if not parsed_group_map:
                source_group_id = schedule.get("source_group_id")
                target_group_id = schedule.get("target_group_id", source_group_id)
                if source_group_id and target_group_id:
                    parsed_group_map = {str(source_group_id): [str(target_group_id)]}
                else:
                    logger.error("Scheduler: No group_map or source/target group configured. Aborting.")
                    return

            # 获取平台实例
            platforms = self.context.platform_manager.get_insts()
            logger.info(f"Scheduler: Found {platforms} platform instances.")
            if not platforms:
                logger.error("Scheduler: No platform instances found. Aborting.")
                return
            # platform = self.context.get_platform(filter.PlatformAdapterType.AIOCQHTTP)
            # if not platform:
            #     logger.error("Scheduler: AiocqhttpAdapter instance not found via context. Aborting.")
            #     return
            # assert isinstance(platform, AiocqhttpAdapter)
            platform = platforms[1]
            platform_name = getattr(platform, "platform", None) or getattr(platform, "name", None) or "aiocqhttp"

            client = platform.get_client() if hasattr(platform, "get_client") else platform
            # 获取当前日期并格式化为 "YYYY年MM月DD日"
            current_date_str = datetime.now().strftime("%Y年%m月%d日")
            # 用于汇总的数据收集
            aggregate_data = []

            for source_group_id, target_group_ids in parsed_group_map.items():
                logger.info(f"Scheduler: Processing source group {source_group_id} -> targets {target_group_ids}")
                # 构造 MockEvent
                class MockEvent:
                    def __init__(self, bot_instance, group_id):
                        self.bot = bot_instance
                        self._group_id = group_id
                    def get_group_id(self):
                        return self._group_id
                mock_event = MockEvent(client, source_group_id)

                # 获取消息
                logger.info(f"Scheduler: Fetching messages for group {source_group_id}...")
                if summary_type == "daily":
                    messages = await self._get_daily_message_history(mock_event)
                else:
                    messages = await self._get_message_history(mock_event, summary_count)
                if not messages:
                    logger.info(f"Scheduler: No messages found in group {source_group_id}. Skipping.")
                    continue

                chat_records = await self._process_messages(mock_event, messages)
                if not chat_records:
                    logger.info(f"Scheduler: No processable content from messages in group {source_group_id}. Skipping.")
                    continue
                TIMESTAMP_PATTERN = re.compile(r"^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]")

                # 检查记录数量，少于20条则跳过
                if len(chat_records) < 20:
                    logger.info(f"Scheduler: Group {source_group_id} has only {len(chat_records)} records (< 20), skipping summary generation.")
                    continue
                processed_chat_records = []
                num_records = len(chat_records)

                for i, record_str in enumerate(chat_records):
                    record_str = str(record_str) # Ensure it's a string
                    match = TIMESTAMP_PATTERN.match(record_str)

                    if match:
                        # If it's the first message (index 0) or the last message in the group
                        if i == 0 or i == num_records - 1:
                            processed_chat_records.append(record_str) # Keep the timestamp
                        else:
                            # For intermediate messages, remove the timestamp
                            processed_record = record_str[match.end():]
                            processed_chat_records.append(processed_record)
                    else:
                        # If it doesn't match the timestamp pattern, append as is
                        processed_chat_records.append(record_str)

                # Replace the original chat_records with the processed version
                chat_records = processed_chat_records
                # --- END OF TIMESTAMP REMOVAL LOGIC ---

                logger.info(f"Scheduler: Group {source_group_id} has {len(chat_records)} processed records.")
                logger.info(f"First 5 processed items: {chat_records[:5]}")
                logger.info(f"Last 5 processed items: {chat_records[-5:]}")
                logger.info("Scheduler: Generating summary...")
                summary_text = await self._generate_summary(chat_records)
                logger.info("Scheduler: Summary generated.")

                # 保存群聊总结到文件
                await self._save_aggregate_to_file(summary_text, "group", source_group_id)

                # 发送到所有目标群
                for target_group_id in target_group_ids:
                    umo = f"{platform_name}:GroupMessage:{target_group_id}"
                    tips=f"#群聊太长不看版/每日群聊总结"
                    if as_image:
                        # markdown转html
                        url = await self._text_to_image(summary_text)

                        message_chain = MessageChain().message(tips).url_image(url)
                    else:
                        message_chain = MessageChain().message( tips+'\n'+summary_text)
                    logger.info(f"Scheduler: Sending summary to UMO: {umo}")
                    await self.context.send_message(umo, message_chain)
                    logger.info(f"Scheduler: Successfully sent scheduled summary to group {target_group_id}.")

                # 如果启用汇总功能，收集原始消息数据
                if self.aggregate_enabled and self.aggregate_group_id:
                    aggregate_data.append({
                        'group_id': source_group_id,
                        'summary': summary_text,
                        'chat_records': chat_records
                    })
            # 处理汇总功能
            if self.aggregate_enabled and self.aggregate_group_id and aggregate_data:
                logger.critical("--- RUNNING NEW AGGREGATE LOGIC (V3) ---") # <--- 添加这条
                logger.info("Scheduler: Starting aggregate summary generation...")
                try:
                    logger.info(f"Aggregate data count: {len(aggregate_data)}")
                    for i, group_data in enumerate(aggregate_data):
                        group_id = group_data['group_id']
                        chat_records = group_data.get('chat_records', [])
                        logger.info(f"Group {group_id} has {len(chat_records)} chat records")

                        # 检查chat_records的内容
                        if chat_records and len(chat_records) > 0:
                            logger.info(f"Sample chat record: {str(chat_records[0])[:100]}")
                        else:
                            logger.warning(f"Empty chat_records for group {group_id}")
                    # 合并所有群的原始消息
                    TIMESTAMP_PATTERN = re.compile(r"^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]")
                    all_chat_records = []
                    # 修改汇总生成逻辑
                    all_summaries = []
                    # for group_data in aggregate_data:
                    #     group_id = group_data['group_id']
                    #     summary_text = group_data['summary'] # <--- 直接获取，无需再调用LLM
                    #     # 使用已经生成的单群总结
                    #     #summary_text = await self._generate_summary(group_data['chat_records'])
                    #     all_summaries.append(f"\n=== 群聊 {group_id} 总结 ===\n{summary_text}")

                    # # 使用汇总提示词生成最终汇总
                    # aggregate_summary = await self._generate_aggregate_summary(all_summaries)
                    for group_data in aggregate_data:
                        group_id = group_data['group_id']
                        chat_records = group_data['chat_records']
                        # 为每个群的消息添加群标识
                        group_header = f"\n=== 群聊 {group_id} ===\n"
                        all_chat_records.append(group_header)
                        num_records = len(chat_records)
                        for i, record_str in enumerate(chat_records):
                            # Ensure record_str is a string, as per original code
                            record_str = str(record_str)

                            # Check if the string starts with a timestamp
                            match = TIMESTAMP_PATTERN.match(record_str)

                            if match:
                                # If it's the first message (index 0) or the last message in the group
                                if i == 0 or i == num_records - 1:
                                    # Keep the timestamp
                                    all_chat_records.append(record_str)
                                else:
                                    # For intermediate messages, remove the timestamp
                                    # The timestamp ends at match.end(), so slice from there
                                    # The original format seems to have a space after the timestamp,
                                    # so we just slice from the end of the match.
                                    processed_record = record_str[match.end():]
                                    all_chat_records.append(processed_record)
                            else:
                                # If it's not a chat message starting with a timestamp (e.g., system message, or malformed)
                                # just append it as is.
                                all_chat_records.append(record_str)
                        # 确保 chat_records 中的每个元素都是字符串
                        #all_chat_records.extend([str(record) for record in chat_records])
                    logger.info(f"First few items: {all_chat_records[:5]}")
                    # 使用汇总提示词生成汇总
                    aggregate_summary = await self._generate_aggregate_summary(all_chat_records)
                    if aggregate_summary == "<暂无数据>":
                        logger.info("汇总内容为 '<暂无数据>'，跳过发布到 Discourse。")
                        return # 直接返回，不继续执行后续操作
                    summary_length = len(aggregate_summary)
                    if summary_length < 300:
                        logger.info(f"汇总内容不足 300 字（实际 {summary_length} 字），跳过发布到 Discourse。")
                        return # 直接返回，不继续执行后续操作
                    # 保存汇总结果到文件
                    await self._save_aggregate_to_file(aggregate_summary, "aggregate")
                    await self.post_to_discourse(
                                                aggregate_summary,
                                                title=f"{current_date_str}【PKMer社群日报】"
                                            )
                    # 发送汇总到指定群
                    umo = f"{platform_name}:GroupMessage:{self.aggregate_group_id}"
                    if as_image:
                        url = await self._text_to_image(aggregate_summary)
                        message_chain = MessageChain().url_image(url)
                    else:
                        message_chain = MessageChain().message(aggregate_summary)

                    logger.info(f"Scheduler: Sending aggregate summary to UMO: {umo}")
                    await self.context.send_message(umo, message_chain)
                    logger.info(f"Scheduler: Successfully sent aggregate summary to group {self.aggregate_group_id}.")

                except Exception as e:
                    logger.error(f"Scheduler: Error generating aggregate summary: {e}")

            # 检查是否是周一，如果是则生成周报
            today = datetime.now()
            if today.weekday() == 0:  # 周一
                logger.info("Scheduler: Today is Monday, generating weekly summaries...")
                try:
                    # 为每个源群生成单独的周报并发送到对应目标群
                    all_weekly_summaries = []

                    for source_group_id, target_group_ids in parsed_group_map.items():
                        logger.info(f"Scheduler: Generating weekly summary for group {source_group_id}...")

                        # 生成单个群的周报
                        single_weekly_summary = await self._generate_weekly_summary(single_group_id=source_group_id)

                        # 保存单群周报到文件
                        await self._save_aggregate_to_file(single_weekly_summary, "weekly", source_group_id)

                        # 收集用于汇总周报
                        all_weekly_summaries.append(f"=== 群聊 {source_group_id} 周报 ===\n{single_weekly_summary}")

                        # 发送到该群对应的所有目标群
                        for target_group_id in target_group_ids:
                            umo = f"{platform_name}:GroupMessage:{target_group_id}"
                            tips=f"#群聊太长不看版/每周群聊总结"
                            if as_image:
                                url = await self._text_to_image(single_weekly_summary)
                                message_chain = MessageChain().message(tips).url_image(url)
                            else:
                                message_chain = MessageChain().message(f"{tips}\n📊 群聊 {source_group_id} 本周周报\n\n{single_weekly_summary}")

                            logger.info(f"Scheduler: Sending weekly summary for group {source_group_id} to UMO: {umo}")
                            await self.context.send_message(umo, message_chain)
                            logger.info(f"Scheduler: Successfully sent weekly summary to target group {target_group_id}.")

                    # 生成汇总周报并发送到汇总群
                    if self.aggregate_enabled and self.aggregate_group_id and all_weekly_summaries:
                        logger.info("Scheduler: Generating aggregate weekly summary...")

                        # 生成所有群的汇总周报
                        source_group_ids = list(parsed_group_map.keys())
                        aggregate_weekly_summary = await self._generate_weekly_summary(group_ids=source_group_ids)

                        # 保存汇总周报到文件 【PKMer社区周报】[YYYY年MM月DD日]
                        await self._save_aggregate_to_file(aggregate_weekly_summary, "weekly")


                        # 调用函数，将格式化后的日期字符串传递给 title 参数
                        await self.post_to_discourse(
                            aggregate_weekly_summary,
                            title=f"【PKMer社区周报】[{current_date_str}]"
                        )
                        # 发送汇总周报到汇总群
                        umo = f"{platform_name}:GroupMessage:{self.aggregate_group_id}"
                        if as_image:
                            url = await self._text_to_image(aggregate_weekly_summary)
                            message_chain = MessageChain().url_image(url)
                        else:
                            message_chain = MessageChain().message(f"📊 本周汇总周报\n\n{aggregate_weekly_summary}")

                        logger.info(f"Scheduler: Sending aggregate weekly summary to UMO: {umo}")
                        await self.context.send_message(umo, message_chain)
                        logger.info(f"Scheduler: Successfully sent aggregate weekly summary to group {self.aggregate_group_id}.")

                except Exception as e:
                    logger.error(f"Scheduler: Error generating weekly summaries: {e}")

        except Exception as e:
            logger.error(f"Scheduler: An unexpected error occurred during scheduled run: {e}", exc_info=True)

    async def _get_daily_message_history(self, event) -> List[Dict[str, Any]]:
        """
        智能获取"昨天凌晨到今天凌晨"的群消息历史

        通过分析凌晨时段(0:00-7:00)的消息间隔，找出每天的自然分界点，
        然后获取从昨天该时间点到今天该时间点的所有消息。
        """
        # 当前时间
        now = datetime.now()
        today_date = now.date()
        yesterday_date = today_date - timedelta(days=1)

        # 凌晨时段的定义(用于寻找分界点)
        early_morning_start = 0  # 凌晨0点
        early_morning_end = 7    # 早上7点

        try:
            # 第一步：获取足够多的历史消息以覆盖至少两天
            all_messages = []
            batch_size = min(100, self.max_records)

            # 首先获取最新的一批消息
            latest_messages = await self._get_message_history(
                event,
                count=batch_size,
                message_seq=0,
                reverse_order=False
            )

            if not latest_messages:
                logger.warning("未获取到任何消息历史")
                return []

            all_messages.extend(latest_messages)

            # 继续获取更多历史消息，直到覆盖至少两天前
            two_days_ago = datetime.combine(today_date - timedelta(days=2), dt_time.min)
            oldest_msg_time = min([msg.get('time', now.timestamp()) for msg in all_messages])
            oldest_msg_datetime = datetime.fromtimestamp(oldest_msg_time)

            # 如果最早的消息还不够早，继续获取
            attempts = 0
            max_attempts = 5  # 限制API调用次数

            while oldest_msg_datetime > two_days_ago and attempts < max_attempts:
                oldest_msg = min(all_messages, key=lambda x: x.get('time', 0))
                oldest_msg_seq = oldest_msg.get('message_seq', 0)

                if oldest_msg_seq <= 0:
                    break

                # 获取更早的消息
                earlier_messages = await self._get_message_history(
                    event,
                    count=batch_size * 2,
                    message_seq=oldest_msg_seq,
                    reverse_order=True
                )

                if not earlier_messages:
                    break

                # 合并消息，确保不重复
                seen_ids = {msg.get('message_id', i) for i, msg in enumerate(all_messages)}
                for msg in earlier_messages:
                    msg_id = msg.get('message_id', None)
                    if msg_id not in seen_ids:
                        all_messages.append(msg)
                        seen_ids.add(msg_id)

                # 更新最早消息时间
                oldest_msg_time = min([msg.get('time', now.timestamp()) for msg in all_messages])
                oldest_msg_datetime = datetime.fromtimestamp(oldest_msg_time)
                attempts += 1

            # 确保消息按时间排序（从新到旧）
            all_messages.sort(key=lambda x: x.get('time', 0), reverse=True)

            # 第二步：分析凌晨时段的消息，找出自然日分界点
            # 转换时间戳为datetime对象
            msg_datetimes = [datetime.fromtimestamp(msg.get('time', 0)) for msg in all_messages]

            # 按日期分组消息
            messages_by_date = {}
            for dt in msg_datetimes:
                date_key = dt.date()
                if date_key not in messages_by_date:
                    messages_by_date[date_key] = []
                messages_by_date[date_key].append(dt)

            # 分析每天凌晨时段的消息间隔
            morning_gaps = []

            for date, times in messages_by_date.items():
                # 只选择凌晨时段的消息
                morning_times = [t for t in times if early_morning_start <= t.hour < early_morning_end]
                morning_times.sort()  # 按时间升序排序

                # 计算凌晨消息之间的间隔
                for i in range(len(morning_times) - 1):
                    curr_time = morning_times[i]
                    next_time = morning_times[i + 1]
                    gap_minutes = (next_time - curr_time).total_seconds() / 60

                    # 只关注较长的间隔(超过30分钟)
                    if gap_minutes >= 30:
                        # 记录(间隔时长, 日期, 间隔结束时间)
                        morning_gaps.append((gap_minutes, date, next_time))

            # 找出最佳的自然日分界时间点
            if morning_gaps:
                # 按间隔时长排序
                morning_gaps.sort(reverse=True)

                # 取最长间隔对应的时间点
                best_gap = morning_gaps[0]
                best_hour = best_gap[2].hour
                best_minute = best_gap[2].minute

                logger.info(f"找到最佳自然日分界点: 凌晨{best_hour}:{best_minute:02d}, 间隔: {best_gap[0]:.1f}分钟")
            else:
                # 如果没有找到明显间隔，使用凌晨4点作为默认分界点
                best_hour = 4
                best_minute = 0
                logger.info(f"未找到明显的凌晨间隔，使用默认分界点: 凌晨{best_hour}:{best_minute:02d}")

            # 第三步：根据找到的分界时间，确定昨天和今天的起止时间
            # 今天的分界时间点
            today_cutoff = datetime.combine(today_date, dt_time(hour=best_hour, minute=best_minute))

            # 如果当前时间还没到今天的分界点，则使用昨天的分界点到现在
            if now < today_cutoff:
                # 昨天的分界时间点到现在
                start_time = datetime.combine(yesterday_date, dt_time(hour=best_hour, minute=best_minute))
                end_time = now
                logger.info(f"当前时间未到今天分界点，获取昨天{best_hour}:{best_minute:02d}到现在的消息")
            else:
                # 昨天的分界时间点到今天的分界时间点
                start_time = datetime.combine(yesterday_date, dt_time(hour=best_hour, minute=best_minute))
                end_time = today_cutoff
                logger.info(f"当前时间已过今天分界点，获取昨天{best_hour}:{best_minute:02d}到今天{best_hour}:{best_minute:02d}的消息")

            # 过滤出指定时间范围内的消息
            start_timestamp = start_time.timestamp()
            end_timestamp = end_time.timestamp()

            daily_messages = [
                msg for msg in all_messages
                if start_timestamp <= msg.get('time', 0) <= end_timestamp
            ]

            # 检查是否获取到了足够早的消息
            complete_data = oldest_msg_datetime <= start_time

            logger.info(f"获取到自然日消息 {len(daily_messages)}/{len(all_messages)} 条，"
                    f"时间范围: {start_time} 到 {end_time}，"
                    f"数据{'完整' if complete_data else '不完整'}")

            return daily_messages

        except Exception as e:
            logger.error(f"获取日消息历史时出错: {e}", exc_info=True)
            # 出错时尝试使用简单方法获取今天的消息
            try:
                # 默认使用凌晨4点作为分界点
                today_4am = datetime.combine(today_date, dt_time(hour=4))
                yesterday_4am = datetime.combine(yesterday_date, dt_time(hour=4))

                # 如果当前时间还没到今天凌晨4点，则获取昨天凌晨4点到现在的消息
                if now < today_4am:
                    start_time = yesterday_4am
                    end_time = now
                else:
                    start_time = yesterday_4am
                    end_time = today_4am

                all_messages = await self._get_message_history(event, self.max_records)
                daily_messages = [
                    msg for msg in all_messages
                    if start_time.timestamp() <= msg.get('time', 0) <= end_time.timestamp()
                ]
                logger.info(f"回退方法: 获取到消息 {len(daily_messages)} 条，时间范围: {start_time} 到 {end_time}")
                return daily_messages
            except Exception as e2:
                logger.error(f"回退方法也失败: {e2}")
                return []

    def _load_prompt(self) -> str:
        """从配置文件中加载提示词

        Returns:
            加载的提示词
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('prompt', 'Default prompt')
            return 'Default prompt'
        except Exception as e:
            logger.error(f"Error loading prompt: {e}")
            return 'Default prompt'
    def _load_prompt_aggregate(self) -> str:
        """从配置文件中加载提示词

        Returns:
            加载的提示词
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('aggregate_prompt', 'Default prompt')
            return 'Default prompt'
        except Exception as e:
            logger.error(f"Error loading prompt: {e}")
            return 'Default prompt'

    def _is_admin(self, user_id: str) -> bool:
        """检查用户是否是管理员

        Args:
            user_id: 用户ID

        Returns:
            是否为管理员
        """
        try:
            if os.path.exists(self.admin_config_path):
                with open(self.admin_config_path, 'r', encoding='utf-8') as f:
                    admin_config = json.load(f)
                    return user_id in admin_config.get('admins_id', [])
            return False
        except Exception as e:
            logger.error(f"Error checking admin status: {e}")
            return False

    async def _is_admin(self, event) -> bool:
        """异步检查用户是否是管理员

        Args:
            event: 消息事件

        Returns:
            是否为管理员
        """
        try:
            user_id = event.get_sender_id()
            return self._is_admin(user_id)
        except:
            return False  # 在测试环境中返回false

    def _extract_message_text(self, message_segments: List[Dict[str, Any]]) -> str:
        """提取消息文本

        Args:
            message_segments: 消息段列表

        Returns:
            提取的文本内容
        """
        result = ""
        try:
            for segment in message_segments:
                msg_type = segment.get('type', '')
                data = segment.get('data', {})

                if msg_type == 'text':
                    result += data.get('text', '') + ' '
                elif msg_type == 'face':
                    result += '[表情] '
                elif msg_type == 'image':
                    # 如果开启了图片文本提取功能，未来可以调用OCR服务
                    result += '[图片] '
                else:
                    result += f'[{msg_type}] '
        except Exception as e:
            logger.error(f"Error extracting message text: {e}")

        return result

    async def _get_message_history(self, event, count: int, message_seq: int = 0, reverse_order: bool = False) -> List[Dict[str, Any]]:
        """获取消息历史

        Args:
            event: 消息事件
            count: 要获取的消息数量
            message_seq: 起始消息ID，0表示最新消息
            reverse_order: 是否倒序排列（旧->新）

        Returns:
            消息历史列表
        """
        try:
            if not ASTRBOT_AVAILABLE:
                # 在测试环境中返回模拟数据
                base_time = int(time.time())
                # 根据reverse_order调整消息顺序
                if reverse_order:
                    # 旧->新
                    start_idx = message_seq if message_seq > 0 else 0
                    messages = [
                        {
                            'sender': {'nickname': f'测试用户{i}'},
                            'time': base_time - (100 - i) * 300,  # 较早的消息
                            'message': [{'type': 'text', 'data': {'text': f'这是第{i}条测试消息'}}],
                            'message_seq': i
                        }
                        for i in range(start_idx, start_idx + count)
                    ]
                else:
                    # 新->旧
                    start_idx = message_seq if message_seq > 0 else 100  # 假设最新消息ID是100
                    messages = [
                        {
                            'sender': {'nickname': f'测试用户{i}'},
                            'time': base_time - (100 - i) * 300,  # 较新的消息在前
                            'message': [{'type': 'text', 'data': {'text': f'这是第{i}条测试消息'}}],
                            'message_seq': i
                        }
                        for i in range(start_idx, max(0, start_idx - count), -1)
                    ]
                return messages

            # 实际环境中的代码
            group_id = event.get_group_id()

            # 兼容不同cqhttp api
            if hasattr(event.bot, 'get_group_msg_history'):
                messages = await event.bot.get_group_msg_history(
                    group_id=group_id,
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )
            else:
                messages = await event.bot.api.call_action(
                    'get_group_msg_history',
                    group_id=group_id,
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )

            return messages.get('messages', [])
        except Exception as e:
            logger.error(f"Error getting message history: {e}")
            return []

    async def _process_messages(self, event, messages: List[Dict[str, Any]]) -> List[str]:
        """处理消息历史记录

        Args:
            event: 消息事件
            messages: 消息历史列表

        Returns:
            处理后的聊天记录列表
        """
        chat_records = []
        try:
            for msg in messages:
                # 获取发送者昵称
                sender = msg.get('sender', {}).get('nickname', 'Unknown')
                # 获取消息时间
                msg_time = msg.get('time', 0)
                time_str = datetime.fromtimestamp(msg_time).strftime('%Y-%m-%d %H:%M:%S')
                # 获取消息内容
                message = msg.get('message', [])
                text = self._extract_message_text(message)

                # 格式化聊天记录
                chat_records.append(f"[{time_str}]「{sender}」: {text}")

            # 反转消息顺序（从旧到新）
            chat_records.reverse()
            return chat_records
        except Exception as e:
            logger.error(f"Error processing messages: {e}")
            return []

    async def _generate_summary(self, chat_lines: List[str]) -> str:
        """生成聊天总结

        Args:
            chat_lines: 聊天记录行

        Returns:
            生成的总结文本
        """
        try:
            # 加载提示词
            prompt = self.prompt
            source_id = self.schedule_config.get("source_group_id", "")
            # 构建输入文本
            input_text = f"{''.join(chat_lines)}"
            content = f"下面是 {source_id} 群聊的内容：\n{input_text}"
            # 如果没有AstrBot环境，返回模拟响应
            if not ASTRBOT_AVAILABLE:
                return "模拟的总结结果 - 测试环境"  # 用于测试

            # 获取LLM提供商
            #provider = self.context.get_using_provider()
            if not self.llm_provider_id:
                return "未配置LLM提供者，无法生成总结"
            provider = self.context.get_provider_by_id(self.llm_provider_id)
            # 调用LLM生成总结
            # response = await provider.text_chat(
            #     prompt=prompt,
            #     input=input_text,
            #     max_tokens=1024,
            #     temperature=0.7
            # )
            response = await provider.text_chat(
                prompt=prompt,
                contexts=[
                    {"role": "user", "content": content}
                ]
            )
            # 返回生成的总结
            return response.completion_text
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return f"生成总结时出错: {str(e)}"

    async def _generate_aggregate_summary(self, chat_lines: List[str]) -> str:
        """生成汇总总结

        Args:
            chat_lines: 来自多个群的聊天记录列表

        Returns:
            汇总总结文本
        """
        try:
            # 如果没有AstrBot环境，返回模拟响应
            if not ASTRBOT_AVAILABLE:
                return "模拟的汇总总结结果 - 测试环境"

            # 获取LLM提供商
            provider = self.context.get_provider_by_id(self.llm_provider_id)
            if not provider:
                return "错误：未找到有效的LLM提供商"
            chat_lines_str = [str(line) for line in chat_lines]
            input_text = "".join(chat_lines_str)



            # 使用汇总提示词
            prompt = self.aggregate_prompt
            content = f"\n下面是来自多个群聊的内容：\n{input_text}"
            original_content_length = len(content)
            MAX_CONTENT_LENGTH = 50 * 1000
            if original_content_length > MAX_CONTENT_LENGTH:
                logger.warning(
                    f"Content length ({original_content_length} chars) exceeds "
                    f"maximum allowed ({MAX_CONTENT_LENGTH} chars). Truncating content."
                )
                content = content[:MAX_CONTENT_LENGTH]
                logger.info(f"Content truncated to {len(content)} chars.")
            else:
                logger.info(f"Content length is {original_content_length} chars, within limits.")
            # 调用LLM生成汇总
            response = await provider.text_chat(
                prompt=prompt,
                contexts=[
                    {"role": "user", "content": content}
                ]
            )

            #logger.info(f"Generating aggregate summary LLM response {response} .")
                    # 关键检查点：检查API返回结果
            if not response: # 检查是否为空或None
                # 不要返回空字符串，而是抛出异常
                logger.error(f"LLM API returned an empty or invalid response: {response}")
            # 返回生成的汇总
            return response.completion_text
        except Exception as e:
            logger.error(f"Error generating aggregate summary: {e}")
            return f"生成汇总总结时出错: {str(e)}"

    async def _save_aggregate_to_file(self, content: str, save_type: str = "aggregate", group_id: str = None):
        """保存内容到文件

        Args:
            content: 要保存的内容
            save_type: 保存类型 ("aggregate" 汇总, "group" 群聊总结, "weekly" 周报)
            group_id: 群ID（当save_type为"group"时使用）
        """
        try:
            from pathlib import Path

            DATA_DIR = StarTools.get_data_dir('astrbot-enhanced-chatsummary')
            # 创建result文件夹
            #PLUGIN_DIR = Path(__file__).resolve().parent
            #result_dir = PLUGIN_DIR / 'result'
            result_dir = DATA_DIR / 'result'
            if not result_dir.exists():
                result_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created result directory: {result_dir}")

            # 生成文件名
            from datetime import datetime
            current_date = datetime.now().strftime("%Y-%m-%d")

            if save_type == "aggregate":
                filename = f"aggregate_summary_{current_date}.txt"
                header_prefix = "汇总"
            elif save_type == "group" and group_id:
                filename = f"group_summary_{current_date}_{group_id}.txt"
                header_prefix = f"群聊{group_id}总结"
            elif save_type == "weekly":
                # 获取当前周的周一日期
                today = datetime.now()
                days_since_monday = today.weekday()
                monday = today - timedelta(days=days_since_monday)
                week_date = monday.strftime("%Y-%m-%d")

                if group_id:
                    # 单群周报
                    filename = f"weekly_summary_{week_date}_{group_id}.txt"
                    header_prefix = f"群聊{group_id}周报"
                else:
                    # 汇总周报
                    filename = f"weekly_summary_{week_date}.txt"
                    header_prefix = "汇总周报"
            else:
                filename = f"summary_{current_date}.txt"
                header_prefix = "总结"

            filepath = os.path.join(result_dir, filename)

            # 添加时间戳到内容开头
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_content = f"=== {header_prefix}时间: {timestamp} ===\n\n{content}\n\n"

            # 创建新文件
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(formatted_content)

            logger.info(f"{header_prefix} saved to: {filepath}")

        except Exception as e:
            logger.error(f"Error saving {save_type} to file: {e}")

    async def _generate_weekly_summary(self, group_ids: List[str] = None, single_group_id: str = None) -> str:
        """生成周报

        Args:
            group_ids: 需要生成周报的群ID列表（用于汇总周报）
            single_group_id: 单个群ID（用于生成单群周报）

        Returns:
            周报内容
        """
        try:
            # 获取上周的日期范围
            today = datetime.now()
            days_since_monday = today.weekday()
            this_monday = today - timedelta(days=days_since_monday)
            last_monday = this_monday - timedelta(days=7)

            # 收集上周的群聊总结
            weekly_content = []
            DATA_DIR = StarTools.get_data_dir('astrbot-enhanced-chatsummary')
            result_dir = DATA_DIR / 'result'
            # 确定要处理的群ID列表
            target_group_ids = []
            if single_group_id:
                target_group_ids = [single_group_id]
            elif group_ids:
                target_group_ids = group_ids
            else:
                logger.error("No group IDs provided for weekly summary generation")
                return "错误：未提供群ID用于生成周报"

            for group_id in target_group_ids:
                group_summaries = []

                # 搜索上周7天的群聊总结文件
                for i in range(7):
                    date = last_monday + timedelta(days=i)
                    date_str = date.strftime("%Y-%m-%d")
                    filename = f"group_summary_{date_str}_{group_id}.txt"
                    filepath = os.path.join(result_dir, filename)

                    if os.path.exists(filepath):
                        try:
                            with open(filepath, "r", encoding="utf-8") as f:
                                content = f.read()
                                group_summaries.append(f"=== {date_str} ===\n{content}")
                        except Exception as e:
                            logger.error(f"Error reading file {filepath}: {e}")

                if group_summaries:
                    if single_group_id:
                        # 单群周报格式
                        group_weekly = f"=== 群聊 {group_id} 本周总结 ===\n" + "\n".join(group_summaries)
                    else:
                        # 多群汇总周报格式
                        group_weekly = f"\n=== 群聊 {group_id} 周总结 ===\n" + "\n".join(group_summaries)
                    weekly_content.append(group_weekly)

            if not weekly_content:
                if single_group_id:
                    return f"群聊 {single_group_id} 本周暂无总结数据可用于生成周报。"
                else:
                    return "本周暂无群聊总结数据可用于生成周报。"

            # 使用LLM生成周报
            all_content = "\n".join(weekly_content)

            if single_group_id:
                # 单群周报提示词
                weekly_prompt = f"""- Role: 群聊周报分析师
- Background: 你需要基于群聊 {single_group_id} 一周的总结数据，生成一份专门的群聊周报。
- Requirements:
  1. 本周概览：总结本群本周的整体活跃度和主要趋势
  2. 重点话题：提取本周最重要和最有趣的讨论话题
  3. 活跃分析：分析群友参与度和互动情况
  4. 周度变化：分析本周相比往期的变化和发展
  5.经典金句:搜集群友的梗和话
 6. 趋势与洞察：基于本周的活跃趋势和热门话题，潜在的热点讨论进行简要预测和引导.提出1-2个开放性问题
- OutputFormat:
  *   **整体结构**: 使用清晰的二级标题（`##`）作为每个主要部分的标题。
  *   **排版**: 充分利用Markdown语法进行精美排版，提升阅读体验：
      *   **`**加粗**`** 突出重点和关键信息。
      *   `*斜体*` 强调次要信息或引用特定短语。
      *   `> 引用` 块用于引用群聊中的关键发言或重要观点。
      *   `列表（- 或 1.）` 清晰罗列要点或分析维度。
      *   `---` 水平分割线用于分隔不同部分，增强视觉效果。
      *   可以适度使用`行内代码`或`代码块`（如果讨论内容涉及代码或命令）。
  *   **标题建议**: XX群，XX年XX月XX日周报回顾**”
  *   **语气**: 保持专业、严谨的分析基调，同时融入社群运营的亲和力与洞察力。语言生动有趣，避免枯燥的数据堆砌。
  *   **互动**: 在文末或各部分末尾，自然地引导读者参与论坛讨论。论坛地址 https://forum.pkmer.net
- Style: 专业而生动，像是一位资深社群运营在做群聊周度汇报。"""
            else:
                # 多群汇总周报提示词
                weekly_prompt = """- Role: 群聊周报分析师
- Background: 你需要基于一周的群聊总结数据，生成一份综合性的周报。
- Requirements:
  1. 整体概览：总结本周各群的整体活跃度和主要趋势（隐藏群号，用群名称代替）  提炼出最引人注目的整体趋势和亮点，可以是一个令人惊喜的数据，或是一个温馨的瞬间。
      *   开篇要引人入胜，快速抓住读者的注意力。
  2. 热门话题：提取本周最重要和最有趣的讨论话题重点话题( 精选本周最受关注、讨论最激烈或最具启发性的1-3个核心话题。
      *   对每个话题进行深入分析：其背景是什么？参与者主要观点有哪些？产生了哪些有价值的结论或疑问？
      *   **务必引用关键发言或有代表性的观点**（使用Markdown的引用块），让内容更真实、更有现场感。
      *   可以提出与该话题相关的延伸思考或问题，引导论坛成员继续讨论。)
  3. 群聊对比：对比不同群聊的特点和活跃情况(注意保护群聊隐私，隐藏群号，用群名称代替)
  4. 周度变化：分析本周相比往期的变化和发展
  6.趋势与洞察：基于本周的活跃趋势和热门话题，潜在的热点讨论进行简要预测和引导.提出1-2个开放性问题
- OutputFormat:
  *   **整体结构**: 使用清晰的二级标题（`##`）作为每个主要部分的标题。
  *   **排版**: 充分利用Markdown语法进行精美排版，提升阅读体验：
      *   **`**加粗**`** 突出重点和关键信息。
      *   `*斜体*` 强调次要信息或引用特定短语。
      *   `> 引用` 块用于引用群聊中的关键发言或重要观点。
      *   `列表（- 或 1.）` 清晰罗列要点或分析维度。
      *   `---` 水平分割线用于分隔不同部分，增强视觉效果。
      *   可以适度使用`行内代码`或`代码块`（如果讨论内容涉及代码或命令）。
  *   **标题建议**: 帖子标题建议格式为：“**【Pkmer社区周报】本周社区脉动与热门话题回顾**”
  *   **语气**: 保持专业、严谨的分析基调，同时融入社群运营的亲和力与洞察力。语言生动有趣，避免枯燥的数据堆砌。
  *   **互动**: 在文末或各部分末尾，自然地引导读者参与论坛讨论。论坛地址 https://forum.pkmer.net
- Style: 专业而生动，像是一位资深社群运营在做周度汇报。
"""

            # 如果没有AstrBot环境，返回模拟响应
            if not ASTRBOT_AVAILABLE:
                return "模拟的周报总结结果 - 测试环境"

            # 获取LLM提供商
            provider = self.context.get_provider_by_id(self.llm_provider_id)
            if not provider:
                return "错误：未找到有效的LLM提供商"

            content = f"\n以下是本周的群聊总结数据：\n{all_content}"

            # 调用LLM生成周报
            response = await provider.text_chat(
                prompt=weekly_prompt,
                contexts=[
                    {"role": "user", "content": content}
                ]
            )
            return response.completion_text

        except Exception as e:
            logger.error(f"Error generating weekly summary: {e}")
            return f"生成周报时出错: {str(e)}"

    @filter.command("消息总结")
    async def summary(self, event, count: Optional[int] = None, debug: Optional[str] = None):
        """触发消息总结，命令加空格，后面跟获取聊天记录的数量

        Args:
            event: 消息事件
            count: 要获取的聊天记录数量
            debug: 调试参数，输入"debug"开启调试模式
        """
        schedule = self.schedule_config
        as_image = schedule.get("as_image", False)
        # 检查参数
        if count is None:
            if hasattr(event, 'plain_result'):
                yield event.plain_result("请提供要获取的消息数量，例如：消息总结 100")
            if hasattr(event, 'stop_event'):
                event.stop_event()
            return

        # 检查记录数量是否超过最大限制
        if count > self.max_records:
            if hasattr(event, 'plain_result'):
                yield event.plain_result(f"请求的消息数量 {count} 超过了最大限制 {self.max_records}")
            count = self.max_records

        # 检查debug参数
        is_debug = debug == "debug" or self.debug_mode
        if is_debug:
            # 检查是否有管理员权限
            if not await self._is_admin(event):
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("只有管理员可以使用调试模式")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return

        # 获取消息历史
        try:
            messages = await self._get_message_history(event, count)
            if not messages:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("未找到消息历史记录")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return

            # 处理消息历史记录
            chat_records = await self._process_messages(event, messages)
            if not chat_records:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("未找到有效的消息记录")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return

            # 如果是调试模式，输出原始记录
            if is_debug:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("调试模式：原始消息记录" + "\n\n" + "\n".join(chat_records))

            # 调用LLM生成总结
            summary = await self._generate_summary(chat_records)
            if as_image:
                        # markdown转html
                url = await self._text_to_image(summary)
                yield event.image_result(url)
                return
            # 发送总结结果
            if hasattr(event, 'plain_result'):
                yield event.plain_result(summary)
            else:
                yield summary
            return

        except Exception as e:
            logger.error(f"Error in summary command: {str(e)}")
            if hasattr(event, 'plain_result'):
                yield event.plain_result(f"生成总结时出错: {str(e)}")
            if hasattr(event, 'stop_event'):
                event.stop_event()
            return
    async def post_to_discourse(self,
    raw_markdown_text: str,
    title: str = None,
    category_id: str = '37', # 可以是分类的 slug 或者分类的 ID
    discourse_url: str = 'https://forum.pkmer.net',
    api_key: str = 'ce78b7200334b9d8f77c4f7edfafd93260b3ddb4da6b69876f153d06e7243402',
    api_username: str = 'Pkmer_bot'
) -> dict:
        """
        通过 Discourse API 发布新主题。

        Args:
            raw_markdown_text (str): 要发布的主题内容，Markdown 格式。
            title (str, optional): 主题的标题。如果为 None，则默认为 "YYYY年MM月DD日 PKMer社区日报"。
            category_id (str, optional): 帖子所属的分类 ID 或 slug。默认为 '37'。
            discourse_url (str, optional): Discourse 论坛的 URL。默认为 'https://forum.pkmer.net'。
            api_key (str, optional): Discourse API 密钥。
            api_username (str, optional): Discourse API 用户名。

        Returns:
            dict: 如果成功，返回 Discourse API 的 JSON 响应；如果失败，返回 None 并打印错误信息。
        """

        # 如果标题未提供，则生成一个默认标题
        if title is None:
            title = datetime.now().strftime("%Y年%m月%d日") + ' PKMer社群日报'

        # 构建 API 请求的 URL
        posts_url = f"{discourse_url}/posts"

        # 构建 POST 请求的数据体
        payload = {
            'title': title,
            'raw': raw_markdown_text,
            'category': category_id
        }

        # 构建 HTTP 请求头
        headers = {
            'Api-Key': api_key,
            'Api-Username': api_username,
            # requests 库在 data 参数是字典时，会自动设置 Content-Type 为 application/x-www-form-urlencoded
            # 'Content-Type': 'application/x-www-form-urlencoded' # 通常不需要显式设置
        }

        try:
            # 发送 POST 请求
            response = requests.post(posts_url, headers=headers, data=payload)

            # 检查响应状态码，如果不是 2xx，则会抛出 HTTPError
            response.raise_for_status()

            # 返回 API 的 JSON 响应
            return response.json()

        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP 错误发生: {http_err}")
            logger.info(f"响应内容: {response.text}")
            return None
        except requests.exceptions.ConnectionError as conn_err:
            logger.error(f"连接错误: {conn_err}")
            return None
        except requests.exceptions.Timeout as timeout_err:
            logger.error(f"请求超时: {timeout_err}")
            return None
        except requests.exceptions.RequestException as req_err:
            logger.error(f"其他请求错误: {req_err}")
            return None
# 为了兼容测试，提供ChatSummary别名
ChatSummary = EnhancedChatSummary
