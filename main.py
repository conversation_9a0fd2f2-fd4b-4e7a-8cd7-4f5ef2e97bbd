"""
聊天记录总结插件 - 增强版
提供智能的聊天记录总结功能，支持多平台和多语言
"""

import os
import json
from datetime import datetime, time as dt_time,timedelta
import logging
from typing import List, Dict, Any, Optional, Union, Type
import time
import markdown
# 尝试导入AstrBot和调度器依赖
try:
    from astrbot.api.event import filter, AstrMessageEvent, MessageChain
    from astrbot.api.star import Context, Star, register
    import astrbot.api.message_components as Comp
    from astrbot.api import logger
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    ASTRBOT_AVAILABLE = True
except ImportError:
    ASTRBOT_AVAILABLE = False
    # 定义模拟的函数和类
    class MockContext:
        """模拟的Context类，用于测试环境"""
        def get_using_provider(self):
            return MockProvider()

    class MockProvider:
        """模拟的Provider类，用于测试环境"""
        async def text_chat(self, input, max_tokens=None, temperature=None):
            class MockResponse:
                completion_text = "模拟的总结结果 - 测试环境"
            return MockResponse()

    class MockStar:
        """模拟的Star类，用于测试环境"""
        def __init__(self, context=None):
            self.context = context or MockContext()
            
    Context = MockContext
    Star = MockStar
    filter = type('MockFilter', (), {'command': lambda x: lambda y: y})
    register = lambda *args, **kwargs: lambda cls: cls
    CronTrigger = None
    AsyncIOScheduler = None


# 导入国际化支持
import sys
# 添加当前目录到Python路径，确保可以导入本地的i18n模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from i18n import I18n

# 设置日志
#logger = logging.getLogger("astrbot.plugin.chatsummary")

# 插件类定义
@register("astrbot_enhanced_chatsummary", "jokeryuyc", 
         "增强版聊天记录总结插件，支持多语言和更多功能", "1.0.3", 
         "https://github.com/jokeryuyc/astrbot-enhanced-chatsummary")
class EnhancedChatSummary(MockStar if not ASTRBOT_AVAILABLE else Star):
    """聊天记录总结插件主类，提供智能的聊天记录总结功能"""
    
    def __init__(self, context: Any, config: Dict[str, Any] = None):
        """初始化插件实例
        
        Args:
            context: AstrBot上下文
            config: 插件配置
        """
        if ASTRBOT_AVAILABLE:
            super().__init__(context)
        else:
            super().__init__(context)
        
        self.context = context
        self.config = config or {}
        
        # 初始化i18n
        self.i18n = I18n(self.config.get("language", "zh_CN"))
        
        # 获取配置项
        self.max_records = self.config.get("max_records", 300)
        self.extract_image_text = self.config.get("extract_image_text", False)
        self.debug_mode = self.config.get("debug_mode", {}).get("enabled", False)
        self.llm_provider_id=self.config.get("llm_provider_id", "")
        self.schedule_config = self.config.get("schedule", {})
        # 配置文件路径
        self.config_path = os.path.join('data', 'config', 'config.json')
        self.admin_config_path = os.path.join('data', 'config', 'admin_config.json')

        # 使用 print 而不是 logger 来避免日志格式化问题
        logger.info(f"EnhancedChatSummary plugin initialized with max_records={self.max_records}")

        # 初始化并启动调度器
        if ASTRBOT_AVAILABLE and self.schedule_config.get("enabled", False):
            self.scheduler = AsyncIOScheduler(timezone="Asia/Shanghai")
            cron_str = self.schedule_config.get("cron", "0 22 * * *")
            try:
                self.scheduler.add_job(
                    self._run_scheduled_summary,
                    trigger=CronTrigger.from_crontab(cron_str, timezone="Asia/Shanghai")
                )
                self.scheduler.start()
                logger.info(f"Scheduled summary job added with cron: {cron_str}")
            except Exception as e:
                logger.error(f"Failed to add scheduled job: {e}")

    async def terminate(self):
        """插件终止时关闭调度器"""
        if hasattr(self, 'scheduler') and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("Scheduler shut down.")
    async def _text_to_image(self,content):
        html_content = markdown.markdown(content, extensions=[
                            'fenced_code', 'tables', 'codehilite', 'sane_lists'
                        ])
        tmpl = """
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
                        
                        :root {
                            --primary-color: #4A6FFF;
                            --primary-light: #EEF2FF;
                            --accent-color: #FF6B6B;
                            --bg-color: #FFFFFF;
                            --text-color: #333344;
                            --secondary-text: #666677;
                            --border-color: #E5E8FF;
                            --code-bg: #2E3440;
                            --code-color: #ECEFF4;
                            --shadow-sm: 0 2px 8px rgba(74, 111, 255, 0.08);
                            --shadow-md: 0 4px 12px rgba(74, 111, 255, 0.12);
                        }
                        
                        * {
                            box-sizing: border-box;
                            margin: 0;
                            padding: 0;
                        }
                        
                        body {
                            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
                            font-size: 20px;
                            line-height: 1.7;
                            color: var(--text-color);
                            background: var(--bg-color);
                            width: 750px;
                            overflow-x: hidden;
                        }
                        
                        .container {
                            width: 100%;
                            background: white;
                        }
                        
                        .header {
                            background: var(--primary-color);
                            padding: 25px 30px;
                            color: white;
                            position: relative;
                            overflow: hidden;
                        }
                        
                        .header::after {
                            content: '';
                            position: absolute;
                            top: 0;
                            right: 0;
                            width: 120px;
                            height: 120px;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            transform: translate(30%, -30%);
                        }
                        
                        .header::before {
                            content: '';
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            width: 80px;
                            height: 80px;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 50%;
                            transform: translate(-30%, 30%);
                        }
                        
                        .content {
                            padding: 30px 40px;
                        }
                        
                        /* 标题样式 */
                        h1, h2, h3, h4, h5, h6 {
                            font-weight: 700;
                            line-height: 1.3;
                            margin-top: 1.4em;
                            margin-bottom: 0.7em;
                            color: var(--primary-color);
                        }
                        
                        h1 {
                            font-size: 28px;
                            margin-top: 0.7em;
                            padding-bottom: 12px;
                            border-bottom: 2px solid var(--primary-light);
                        }
                        
                        h2 {
                            font-size: 24px;
                            padding-left: 14px;
                            border-left: 4px solid var(--primary-color);
                        }
                        
                        h3 {
                            font-size: 22px;
                            color: var(--accent-color);
                        }
                        
                        /* 段落样式 */
                        p {
                            margin: 0 0 18px;
                            word-break: break-word;
                        }
                        
                        /* 表格样式 */
                        table {
                            width: 100%;
                            margin: 24px 0;
                            border-collapse: collapse;
                            border-radius: 8px;
                            overflow: hidden;
                            box-shadow: var(--shadow-sm);
                            font-size: 17px;
                        }
                        
                        th, td {
                            padding: 14px 18px;
                            border: 1px solid var(--border-color);
                        }
                        
                        th {
                            background-color: var(--primary-light);
                            color: var(--primary-color);
                            font-weight: 500;
                            text-align: left;
                        }
                        
                        tr:nth-child(even) {
                            background-color: rgba(238, 242, 255, 0.5);
                        }
                        
                        /* 代码块样式 */
                        pre {
                            background-color: var(--code-bg);
                            color: var(--code-color);
                            padding: 18px 20px;
                            border-radius: 10px;
                            font-family: 'JetBrains Mono', 'Fira Code', 'Menlo', 'Monaco', monospace;
                            font-size: 16px;
                            overflow-x: auto;
                            margin: 22px 0;
                            box-shadow: var(--shadow-sm);
                            position: relative;
                        }
                        
                        pre::before {
                            content: '';
                            position: absolute;
                            top: 14px;
                            left: 14px;
                            width: 12px;
                            height: 12px;
                            background: #FF5F56;
                            border-radius: 50%;
                            box-shadow: 20px 0 0 #FFBD2E, 40px 0 0 #27C93F;
                        }
                        
                        code {
                            font-family: 'JetBrains Mono', 'Fira Code', 'Menlo', 'Monaco', monospace;
                            background: var(--primary-light);
                            color: var(--primary-color);
                            padding: 3px 6px;
                            border-radius: 4px;
                            font-size: 0.9em;
                        }
                        
                        pre code {
                            background: transparent;
                            color: inherit;
                            padding: 0;
                            border-radius: 0;
                            padding-top: 12px;
                            display: block;
                        }
                        
                        /* 图片样式 */
                        img {
                            max-width: 100%;
                            height: auto;
                            border-radius: 12px;
                            display: block;
                            margin: 24px auto;
                            box-shadow: var(--shadow-md);
                        }
                        
                        /* 列表样式 */
                        ul, ol {
                            margin: 18px 0;
                            padding-left: 28px;
                        }
                        
                        li {
                            margin-bottom: 10px;
                            position: relative;
                        }
                        
                        ul li::marker {
                            color: var(--primary-color);
                        }
                        
                        /* 链接样式 */
                        a {
                            color: var(--primary-color);
                            text-decoration: none;
                            font-weight: 500;
                            border-bottom: 1px solid transparent;
                            transition: border-color 0.2s;
                        }
                        
                        a:hover {
                            border-bottom-color: var(--primary-color);
                        }
                        
                        /* 引用样式 */
                        blockquote {
                            border-left: 4px solid var(--accent-color);
                            color: var(--secondary-text);
                            padding: 15px 20px;
                            margin: 22px 0;
                            background: #FFF8F8;
                            border-radius: 0 8px 8px 0;
                            font-style: italic;
                        }
                        
                        blockquote p:last-child {
                            margin-bottom: 0;
                        }
                        
                        /* 分割线 */
                        hr {
                            border: 0;
                            height: 2px;
                            background: linear-gradient(to right, var(--border-color), var(--primary-light), var(--border-color));
                            margin: 30px 0;
                        }
                        
                        /* 底部装饰 */
                        .footer {
                            text-align: center;
                            padding: 18px;
                            color: var(--secondary-text);
                            font-size: 16px;
                            background: var(--primary-light);
                            border-top: 1px solid var(--border-color);
                        }
                        
                        /* 代码高亮 */
                        .codehilite .hll { background-color: #49483e }
                        .codehilite .c { color: #75715e } /* Comment */
                        .codehilite .err { color: #f92672 } /* Error */
                        .codehilite .k { color: #66d9ef } /* Keyword */
                        .codehilite .l { color: #ae81ff } /* Literal */
                        .codehilite .n { color: #f8f8f2 } /* Name */
                        .codehilite .o { color: #f92672 } /* Operator */
                        .codehilite .p { color: #f8f8f2 } /* Punctuation */
                        .codehilite .ch { color: #75715e } /* Comment.Hashbang */
                        .codehilite .cm { color: #75715e } /* Comment.Multiline */
                        .codehilite .cp { color: #75715e } /* Comment.Preproc */
                        .codehilite .c1 { color: #75715e } /* Comment.Single */
                        .codehilite .cs { color: #75715e } /* Comment.Special */
                        .codehilite .gd { color: #f92672 } /* Generic.Deleted */
                        .codehilite .ge { font-style: italic } /* Generic.Emph */
                        .codehilite .gi { color: #a6e22e } /* Generic.Inserted */
                        .codehilite .gs { font-weight: bold } /* Generic.Strong */
                        .codehilite .gu { color: #75715e } /* Generic.Subheading */
                        .codehilite .kc { color: #66d9ef } /* Keyword.Constant */
                        .codehilite .kd { color: #66d9ef } /* Keyword.Declaration */
                        .codehilite .kn { color: #f92672 } /* Keyword.Namespace */
                        .codehilite .kp { color: #66d9ef } /* Keyword.Pseudo */
                        .codehilite .kr { color: #66d9ef } /* Keyword.Reserved */
                        .codehilite .kt { color: #66d9ef } /* Keyword.Type */
                        .codehilite .ld { color: #e6db74 } /* Literal.Date */
                        .codehilite .m { color: #ae81ff } /* Literal.Number */
                        .codehilite .s { color: #e6db74 } /* Literal.String */
                        .codehilite .na { color: #a6e22e } /* Name.Attribute */
                        .codehilite .nb { color: #f8f8f2 } /* Name.Builtin */
                        .codehilite .nc { color: #a6e22e } /* Name.Class */
                        .codehilite .no { color: #66d9ef } /* Name.Constant */
                        .codehilite .nd { color: #a6e22e } /* Name.Decorator */
                        .codehilite .ni { color: #f8f8f2 } /* Name.Entity */
                        .codehilite .ne { color: #a6e22e } /* Name.Exception */
                        .codehilite .nf { color: #a6e22e } /* Name.Function */
                        .codehilite .nl { color: #f8f8f2 } /* Name.Label */
                        .codehilite .nn { color: #f8f8f2 } /* Name.Namespace */
                        .codehilite .nx { color: #a6e22e } /* Name.Other */
                        .codehilite .py { color: #f8f8f2 } /* Name.Property */
                        .codehilite .nt { color: #f92672 } /* Name.Tag */
                        .codehilite .nv { color: #f8f8f2 } /* Name.Variable */
                        .codehilite .ow { color: #f92672 } /* Operator.Word */
                        .codehilite .w { color: #f8f8f2 } /* Text.Whitespace */
                        .codehilite .mb { color: #ae81ff } /* Literal.Number.Bin */
                        .codehilite .mf { color: #ae81ff } /* Literal.Number.Float */
                        .codehilite .mh { color: #ae81ff } /* Literal.Number.Hex */
                        .codehilite .mi { color: #ae81ff } /* Literal.Number.Integer */
                        .codehilite .mo { color: #ae81ff } /* Literal.Number.Oct */
                        .codehilite .sa { color: #e6db74 } /* Literal.String.Affix */
                        .codehilite .sb { color: #e6db74 } /* Literal.String.Backtick */
                        .codehilite .sc { color: #e6db74 } /* Literal.String.Char */
                        .codehilite .dl { color: #e6db74 } /* Literal.String.Delimiter */
                        .codehilite .sd { color: #e6db74 } /* Literal.String.Doc */
                        .codehilite .s2 { color: #e6db74 } /* Literal.String.Double */
                        .codehilite .se { color: #ae81ff } /* Literal.String.Escape */
                        .codehilite .sh { color: #e6db74 } /* Literal.String.Heredoc */
                        .codehilite .si { color: #e6db74 } /* Literal.String.Interpol */
                        .codehilite .sx { color: #e6db74 } /* Literal.String.Other */
                        .codehilite .sr { color: #e6db74 } /* Literal.String.Regex */
                        .codehilite .s1 { color: #e6db74 } /* Literal.String.Single */
                        .codehilite .ss { color: #e6db74 } /* Literal.String.Symbol */
                        .codehilite .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */
                        .codehilite .fm { color: #a6e22e } /* Name.Function.Magic */
                        .codehilite .vc { color: #f8f8f2 } /* Name.Variable.Class */
                        .codehilite .vg { color: #f8f8f2 } /* Name.Variable.Global */
                        .codehilite .vi { color: #f8f8f2 } /* Name.Variable.Instance */
                        .codehilite .vm { color: #f8f8f2 } /* Name.Variable.Magic */
                        .codehilite .il { color: #ae81ff } /* Literal.Number.Integer.Long */
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1 style="color: white; margin: 0; border: none; font-size: 24px;">群聊太长不看版</h1>
                        </div>
                        <div class="content">
                            {{ html_content }}
                        </div>
                        <div class="footer">
                            由 PKMer AI 助手生成 • 仅供参考 • {{date_str}}
                        </div>
                    </div>
                </body>
                </html>
            """
        render_options = {
            "type": "jpeg",
            "quality": 95,
            "full_page": True,
            "clip": {
                "x": 0,
                "y": 0,
                "width": 750,
                "height": 8000
            },
            "scale": "device",
            "animations": "disabled",
            "caret": "hide"
            }
        date_str = datetime.now().strftime("%Y-%m-%d")
        render_data = {"html_content": html_content,"date_str":date_str}
        url = await self.html_render(tmpl, render_data, return_url=True, options=render_options)
        return url
    def safe_parse_json(self,json_str):
        """
        安全解析JSON字符串
        :param json_str: JSON格式的字符串
        :return: 解析后的字典，解析失败返回空字典
        """
        try:
            # 去除首尾空白字符（如果有）
            cleaned_str = json_str.strip()
            # 解析为Python对象
            parsed = json.loads(cleaned_str)
            
            # 类型检查确保是字典
            if not isinstance(parsed, dict):
                raise ValueError("Parsed JSON is not a dictionary")
                
            # 验证数据结构格式
            result = {}
            for key, value in parsed.items():
                # 确保键是字符串或数字
                if not isinstance(key, (str, int)):
                    raise ValueError(f"Invalid key type: {type(key)}")
                    
                # 确保值是列表
                if not isinstance(value, list):
                    raise ValueError(f"Value should be list, got {type(value)}")
                    
                # 统一转换为字符串格式
                result[str(key)] = [str(item) for item in value]
                
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {}
        except ValueError as e:
            logger.error(f"数据格式错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return {}
    async def _run_scheduled_summary(self):
        logger.info("Scheduler: Starting scheduled summary run...")
        try:
            schedule = self.schedule_config
            group_map = schedule.get("group_map")
            as_image = schedule.get("as_image", False)
            summary_type = schedule.get("summary_type", "daily")
            summary_count = schedule.get("summary_count", 200)
            cron_str = schedule.get("cron", "0 22 * * *")
            parsed_group_map = self.safe_parse_json(group_map)
            # 兼容老配置
            if not parsed_group_map:
                source_group_id = schedule.get("source_group_id")
                target_group_id = schedule.get("target_group_id", source_group_id)
                if source_group_id and target_group_id:
                    parsed_group_map = {str(source_group_id): [str(target_group_id)]}
                else:
                    logger.error("Scheduler: No group_map or source/target group configured. Aborting.")
                    return

            # 获取平台实例
            platforms = self.context.platform_manager.get_insts()
            if not platforms:
                logger.error("Scheduler: No platform instances found. Aborting.")
                return
            platform = platforms[1]
            platform_name = getattr(platform, "platform", None) or getattr(platform, "name", None) or "aiocqhttp"

            client = platform.get_client() if hasattr(platform, "get_client") else platform

            for source_group_id, target_group_ids in parsed_group_map.items():
                logger.info(f"Scheduler: Processing source group {source_group_id} -> targets {target_group_ids}")
                # 构造 MockEvent
                class MockEvent:
                    def __init__(self, bot_instance, group_id):
                        self.bot = bot_instance
                        self._group_id = group_id
                    def get_group_id(self):
                        return self._group_id
                mock_event = MockEvent(client, source_group_id)

                # 获取消息
                logger.info(f"Scheduler: Fetching messages for group {source_group_id}...")
                if summary_type == "daily":
                    messages = await self._get_daily_message_history(mock_event)
                else:
                    messages = await self._get_message_history(mock_event, summary_count)
                if not messages:
                    logger.info(f"Scheduler: No messages found in group {source_group_id}. Skipping.")
                    continue

                chat_records = await self._process_messages(mock_event, messages)
                if not chat_records:
                    logger.info(f"Scheduler: No processable content from messages in group {source_group_id}. Skipping.")
                    continue

                logger.info("Scheduler: Generating summary...")
                summary_text = await self._generate_summary(chat_records)
                logger.info("Scheduler: Summary generated.")

                # 发送到所有目标群
                for target_group_id in target_group_ids:
                    umo = f"{platform_name}:GroupMessage:{target_group_id}"
                    if as_image:
                        # markdown转html
                        url = await self._text_to_image(summary_text)
                        
                        message_chain = MessageChain().url_image(url)
                    else:
                        message_chain = MessageChain().message(summary_text)
                    logger.info(f"Scheduler: Sending summary to UMO: {umo}")
                    await self.context.send_message(umo, message_chain)
                    logger.info(f"Scheduler: Successfully sent scheduled summary to group {target_group_id}.")

        except Exception as e:
            logger.error(f"Scheduler: An unexpected error occurred during scheduled run: {e}", exc_info=True)

    async def _get_daily_message_history(self, event) -> List[Dict[str, Any]]:
        """
        智能获取"昨天凌晨到今天凌晨"的群消息历史
        
        通过分析凌晨时段(0:00-7:00)的消息间隔，找出每天的自然分界点，
        然后获取从昨天该时间点到今天该时间点的所有消息。
        """
        # 当前时间
        now = datetime.now()
        today_date = now.date()
        yesterday_date = today_date - timedelta(days=1)
        
        # 凌晨时段的定义(用于寻找分界点)
        early_morning_start = 0  # 凌晨0点
        early_morning_end = 7    # 早上7点
        
        try:
            # 第一步：获取足够多的历史消息以覆盖至少两天
            all_messages = []
            batch_size = min(100, self.max_records)
            
            # 首先获取最新的一批消息
            latest_messages = await self._get_message_history(
                event, 
                count=batch_size, 
                message_seq=0,
                reverse_order=False
            )
            
            if not latest_messages:
                logger.warning("未获取到任何消息历史")
                return []
            
            all_messages.extend(latest_messages)
            
            # 继续获取更多历史消息，直到覆盖至少两天前
            two_days_ago = datetime.combine(today_date - timedelta(days=2), dt_time.min)
            oldest_msg_time = min([msg.get('time', now.timestamp()) for msg in all_messages])
            oldest_msg_datetime = datetime.fromtimestamp(oldest_msg_time)
            
            # 如果最早的消息还不够早，继续获取
            attempts = 0
            max_attempts = 5  # 限制API调用次数
            
            while oldest_msg_datetime > two_days_ago and attempts < max_attempts:
                oldest_msg = min(all_messages, key=lambda x: x.get('time', 0))
                oldest_msg_seq = oldest_msg.get('message_seq', 0)
                
                if oldest_msg_seq <= 0:
                    break
                    
                # 获取更早的消息
                earlier_messages = await self._get_message_history(
                    event,
                    count=batch_size * 2,
                    message_seq=oldest_msg_seq,
                    reverse_order=True
                )
                
                if not earlier_messages:
                    break
                    
                # 合并消息，确保不重复
                seen_ids = {msg.get('message_id', i) for i, msg in enumerate(all_messages)}
                for msg in earlier_messages:
                    msg_id = msg.get('message_id', None)
                    if msg_id not in seen_ids:
                        all_messages.append(msg)
                        seen_ids.add(msg_id)
                
                # 更新最早消息时间
                oldest_msg_time = min([msg.get('time', now.timestamp()) for msg in all_messages])
                oldest_msg_datetime = datetime.fromtimestamp(oldest_msg_time)
                attempts += 1
            
            # 确保消息按时间排序（从新到旧）
            all_messages.sort(key=lambda x: x.get('time', 0), reverse=True)
            
            # 第二步：分析凌晨时段的消息，找出自然日分界点
            # 转换时间戳为datetime对象
            msg_datetimes = [datetime.fromtimestamp(msg.get('time', 0)) for msg in all_messages]
            
            # 按日期分组消息
            messages_by_date = {}
            for dt in msg_datetimes:
                date_key = dt.date()
                if date_key not in messages_by_date:
                    messages_by_date[date_key] = []
                messages_by_date[date_key].append(dt)
            
            # 分析每天凌晨时段的消息间隔
            morning_gaps = []
            
            for date, times in messages_by_date.items():
                # 只选择凌晨时段的消息
                morning_times = [t for t in times if early_morning_start <= t.hour < early_morning_end]
                morning_times.sort()  # 按时间升序排序
                
                # 计算凌晨消息之间的间隔
                for i in range(len(morning_times) - 1):
                    curr_time = morning_times[i]
                    next_time = morning_times[i + 1]
                    gap_minutes = (next_time - curr_time).total_seconds() / 60
                    
                    # 只关注较长的间隔(超过30分钟)
                    if gap_minutes >= 30:
                        # 记录(间隔时长, 日期, 间隔结束时间)
                        morning_gaps.append((gap_minutes, date, next_time))
            
            # 找出最佳的自然日分界时间点
            if morning_gaps:
                # 按间隔时长排序
                morning_gaps.sort(reverse=True)
                
                # 取最长间隔对应的时间点
                best_gap = morning_gaps[0]
                best_hour = best_gap[2].hour
                best_minute = best_gap[2].minute
                
                logger.info(f"找到最佳自然日分界点: 凌晨{best_hour}:{best_minute:02d}, 间隔: {best_gap[0]:.1f}分钟")
            else:
                # 如果没有找到明显间隔，使用凌晨4点作为默认分界点
                best_hour = 4
                best_minute = 0
                logger.info(f"未找到明显的凌晨间隔，使用默认分界点: 凌晨{best_hour}:{best_minute:02d}")
            
            # 第三步：根据找到的分界时间，确定昨天和今天的起止时间
            # 今天的分界时间点
            today_cutoff = datetime.combine(today_date, dt_time(hour=best_hour, minute=best_minute))
            
            # 如果当前时间还没到今天的分界点，则使用昨天的分界点到现在
            if now < today_cutoff:
                # 昨天的分界时间点到现在
                start_time = datetime.combine(yesterday_date, dt_time(hour=best_hour, minute=best_minute))
                end_time = now
                logger.info(f"当前时间未到今天分界点，获取昨天{best_hour}:{best_minute:02d}到现在的消息")
            else:
                # 昨天的分界时间点到今天的分界时间点
                start_time = datetime.combine(yesterday_date, dt_time(hour=best_hour, minute=best_minute))
                end_time = today_cutoff
                logger.info(f"当前时间已过今天分界点，获取昨天{best_hour}:{best_minute:02d}到今天{best_hour}:{best_minute:02d}的消息")
            
            # 过滤出指定时间范围内的消息
            start_timestamp = start_time.timestamp()
            end_timestamp = end_time.timestamp()
            
            daily_messages = [
                msg for msg in all_messages 
                if start_timestamp <= msg.get('time', 0) <= end_timestamp
            ]
            
            # 检查是否获取到了足够早的消息
            complete_data = oldest_msg_datetime <= start_time
            
            logger.info(f"获取到自然日消息 {len(daily_messages)}/{len(all_messages)} 条，" 
                    f"时间范围: {start_time} 到 {end_time}，"
                    f"数据{'完整' if complete_data else '不完整'}")
            
            return daily_messages
        
        except Exception as e:
            logger.error(f"获取日消息历史时出错: {e}", exc_info=True)
            # 出错时尝试使用简单方法获取今天的消息
            try:
                # 默认使用凌晨4点作为分界点
                today_4am = datetime.combine(today_date, dt_time(hour=4))
                yesterday_4am = datetime.combine(yesterday_date, dt_time(hour=4))
                
                # 如果当前时间还没到今天凌晨4点，则获取昨天凌晨4点到现在的消息
                if now < today_4am:
                    start_time = yesterday_4am
                    end_time = now
                else:
                    start_time = yesterday_4am
                    end_time = today_4am
                
                all_messages = await self._get_message_history(event, self.max_records)
                daily_messages = [
                    msg for msg in all_messages 
                    if start_time.timestamp() <= msg.get('time', 0) <= end_time.timestamp()
                ]
                logger.info(f"回退方法: 获取到消息 {len(daily_messages)} 条，时间范围: {start_time} 到 {end_time}")
                return daily_messages
            except Exception as e2:
                logger.error(f"回退方法也失败: {e2}")
                return []

    def _load_prompt(self) -> str:
        """从配置文件中加载提示词
        
        Returns:
            加载的提示词
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('prompt', 'Default prompt')
            return 'Default prompt'
        except Exception as e:
            logger.error(f"Error loading prompt: {e}")
            return 'Default prompt'
    
    def _is_admin(self, user_id: str) -> bool:
        """检查用户是否是管理员
        
        Args:
            user_id: 用户ID
            
        Returns:
            是否为管理员
        """
        try:
            if os.path.exists(self.admin_config_path):
                with open(self.admin_config_path, 'r', encoding='utf-8') as f:
                    admin_config = json.load(f)
                    return user_id in admin_config.get('admins_id', [])
            return False
        except Exception as e:
            logger.error(f"Error checking admin status: {e}")
            return False
            
    async def _is_admin(self, event) -> bool:
        """异步检查用户是否是管理员
        
        Args:
            event: 消息事件
            
        Returns:
            是否为管理员
        """
        try:
            user_id = event.get_sender_id()
            return self._is_admin(user_id)
        except:
            return False  # 在测试环境中返回false

    def _extract_message_text(self, message_segments: List[Dict[str, Any]]) -> str:
        """提取消息文本
        
        Args:
            message_segments: 消息段列表
            
        Returns:
            提取的文本内容
        """
        result = ""
        try:
            for segment in message_segments:
                msg_type = segment.get('type', '')
                data = segment.get('data', {})
                
                if msg_type == 'text':
                    result += data.get('text', '') + ' '
                elif msg_type == 'face':
                    result += '[表情] '
                elif msg_type == 'image':
                    # 如果开启了图片文本提取功能，未来可以调用OCR服务
                    result += '[图片] '
                else:
                    result += f'[{msg_type}] '
        except Exception as e:
            logger.error(f"Error extracting message text: {e}")

        return result
    
    async def _get_message_history(self, event, count: int, message_seq: int = 0, reverse_order: bool = False) -> List[Dict[str, Any]]:
        """获取消息历史
        
        Args:
            event: 消息事件
            count: 要获取的消息数量
            message_seq: 起始消息ID，0表示最新消息
            reverse_order: 是否倒序排列（旧->新）
            
        Returns:
            消息历史列表
        """
        try:
            if not ASTRBOT_AVAILABLE:
                # 在测试环境中返回模拟数据
                base_time = int(time.time())
                # 根据reverse_order调整消息顺序
                if reverse_order:
                    # 旧->新
                    start_idx = message_seq if message_seq > 0 else 0
                    messages = [
                        {
                            'sender': {'nickname': f'测试用户{i}'},
                            'time': base_time - (100 - i) * 300,  # 较早的消息
                            'message': [{'type': 'text', 'data': {'text': f'这是第{i}条测试消息'}}],
                            'message_seq': i
                        }
                        for i in range(start_idx, start_idx + count)
                    ]
                else:
                    # 新->旧
                    start_idx = message_seq if message_seq > 0 else 100  # 假设最新消息ID是100
                    messages = [
                        {
                            'sender': {'nickname': f'测试用户{i}'},
                            'time': base_time - (100 - i) * 300,  # 较新的消息在前
                            'message': [{'type': 'text', 'data': {'text': f'这是第{i}条测试消息'}}],
                            'message_seq': i
                        }
                        for i in range(start_idx, max(0, start_idx - count), -1)
                    ]
                return messages
                
            # 实际环境中的代码
            group_id = event.get_group_id()
            
            # 兼容不同cqhttp api
            if hasattr(event.bot, 'get_group_msg_history'):
                messages = await event.bot.get_group_msg_history(
                    group_id=group_id, 
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )
            else:
                messages = await event.bot.api.call_action(
                    'get_group_msg_history',
                    group_id=group_id,
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )
            
            return messages.get('messages', [])
        except Exception as e:
            logger.error(f"Error getting message history: {e}")
            return []
    
    async def _process_messages(self, event, messages: List[Dict[str, Any]]) -> List[str]:
        """处理消息历史记录
        
        Args:
            event: 消息事件
            messages: 消息历史列表
            
        Returns:
            处理后的聊天记录列表
        """
        chat_records = []
        try:
            for msg in messages:
                # 获取发送者昵称
                sender = msg.get('sender', {}).get('nickname', 'Unknown')
                # 获取消息时间
                msg_time = msg.get('time', 0)
                time_str = datetime.fromtimestamp(msg_time).strftime('%Y-%m-%d %H:%M:%S')
                # 获取消息内容
                message = msg.get('message', [])
                text = self._extract_message_text(message)
                
                # 格式化聊天记录
                chat_records.append(f"[{time_str}]「{sender}」: {text}")
                
            # 反转消息顺序（从旧到新）
            chat_records.reverse()
            return chat_records
        except Exception as e:
            logger.error(f"Error processing messages: {e}")
            return []
    
    async def _generate_summary(self, chat_lines: List[str]) -> str:
        """生成聊天总结
        
        Args:
            chat_lines: 聊天记录行
            
        Returns:
            生成的总结文本
        """
        try:
            # 加载提示词
            prompt = self._load_prompt()
            source_id = self.schedule_config.get("source_group_id", "")
            # 构建输入文本
            input_text = f"{''.join(chat_lines)}"
            content = f"下面是 {source_id} 群聊的内容：\n{input_text}"
            # 如果没有AstrBot环境，返回模拟响应
            if not ASTRBOT_AVAILABLE:
                return "模拟的总结结果 - 测试环境"  # 用于测试
            
            # 获取LLM提供商
            #provider = self.context.get_using_provider()
            if not self.llm_provider_id:
                return "未配置LLM提供者，无法生成总结"
            provider = self.context.get_provider_by_id(self.llm_provider_id)
            # 调用LLM生成总结
            # response = await provider.text_chat(
            #     prompt=prompt,
            #     input=input_text,
            #     max_tokens=1024,
            #     temperature=0.7
            # )
            response = await provider.text_chat(
                prompt=prompt,
                contexts=[
                    {"role": "user", "content": content}
                ]
            )
            # 返回生成的总结
            return response.completion_text
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return f"生成总结时出错: {str(e)}"

    @filter.command("消息总结")
    async def summary(self, event, count: Optional[int] = None, debug: Optional[str] = None):
        """触发消息总结，命令加空格，后面跟获取聊天记录的数量
        
        Args:
            event: 消息事件
            count: 要获取的聊天记录数量
            debug: 调试参数，输入"debug"开启调试模式
        """
        schedule = self.schedule_config
        as_image = schedule.get("as_image", False)
        # 检查参数
        if count is None:
            if hasattr(event, 'plain_result'):
                yield event.plain_result("请提供要获取的消息数量，例如：消息总结 100")
            if hasattr(event, 'stop_event'):
                event.stop_event()
            return
            
        # 检查记录数量是否超过最大限制
        if count > self.max_records:
            if hasattr(event, 'plain_result'):
                yield event.plain_result(f"请求的消息数量 {count} 超过了最大限制 {self.max_records}")
            count = self.max_records
            
        # 检查debug参数
        is_debug = debug == "debug" or self.debug_mode
        if is_debug:
            # 检查是否有管理员权限
            if not await self._is_admin(event):
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("只有管理员可以使用调试模式")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return
                
        # 获取消息历史
        try:
            messages = await self._get_message_history(event, count)
            if not messages:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("未找到消息历史记录")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return
                
            # 处理消息历史记录
            chat_records = await self._process_messages(event, messages)
            if not chat_records:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("未找到有效的消息记录")
                if hasattr(event, 'stop_event'):
                    event.stop_event()
                return
                
            # 如果是调试模式，输出原始记录
            if is_debug:
                if hasattr(event, 'plain_result'):
                    yield event.plain_result("调试模式：原始消息记录" + "\n\n" + "\n".join(chat_records))
                
            # 调用LLM生成总结
            summary = await self._generate_summary(chat_records)
            if as_image:
                        # markdown转html
                url = await self._text_to_image(summary)
                yield event.image_result(url)
                return
            # 发送总结结果
            if hasattr(event, 'plain_result'):
                yield event.plain_result(summary)
            else:
                yield summary
            return
            
        except Exception as e:
            logger.error(f"Error in summary command: {str(e)}")
            if hasattr(event, 'plain_result'):
                yield event.plain_result(f"生成总结时出错: {str(e)}")
            if hasattr(event, 'stop_event'):
                event.stop_event()
            return

# 为了兼容测试，提供ChatSummary别名
ChatSummary = EnhancedChatSummary
