name: astrbot_plugin_chatsummary
desc: 一个基于LLM的智能历史聊天记录总结插件，支持结构化输出和自定义提示词。基于laopanmemz的原始插件astrbot_plugin_chatsummary进行功能增强开发。
help: |
  输入 /消息总结 + [空格] + 要总结的聊天记录数量（仅整数）触发消息总结。
  示例：/消息总结 100
  
  管理员可以使用调试模式：/消息总结 100 debug
  
  快速安装指南：
  1. 使用源码安装（推荐）：
     git clone https://github.com/jokeryuyc/astrbot-enhanced-chatsummary.git
     cd astrbot-enhanced-chatsummary
     pip install -e .
  
  2. 一键安装脚本：
     curl -sSL https://raw.githubusercontent.com/jokeryuyc/astrbot-enhanced-chatsummary/main/install.sh | bash
  
  3. 常见问题解决：
     - 如果出现“找不到包”错误，请使用源码安装方式
     - 详细安装指南请阅读INSTALL.md文件
  
  兼容平台：
  - 微信、QQ、钉钉、飞书等主流聊天平台
  - 平台特性具体说明请参考docs/PLATFORM_COMPATIBILITY.md
  
  插件配置指南：
  - 配置文件位于 data/config/ 目录
  - 详细说明请参考docs/CONFIG.md
version: v1.0.3
author: jokeryuyc
original_author: laopanmemz
original_repo: https://github.com/laopanmemz/astrbot_plugin_chatsummary
original_version: v0.1.0
repo: https://github.com/jokeryuyc/astrbot-enhanced-chatsummary
enhancement_summary: |
  与原始插件相比，本增强版增加了以下功能：
  1. 多平台兼容性：全面支持微信、QQ、钉钉、飞书等平台
  2. 国际化支持：增加多语言界面和提示
  3. 强化错误处理：提供全面的异常捕获和恢复机制
  4. 完善文档系统：新增安装指南、排错手册等
  5. 测试与CI/CD：增强测试覆盖率和自动化流程
platforms:
  - wechat
  - qq
  - dingtalk
  - feishu
platform_compatibility: |
  本插件专门进行了多平台适配和兼容性强化，各平台特性请参考文档。
dependencies:
  - astrbot>=0.1.0
  - jinja2>=3.0.0
  - pyyaml>=6.0.0
  - typing-extensions>=4.0.0
  - loguru>=0.6.0
tags:
  - 聊天
  - 总结
  - LLM
  - 工具
  - 多平台
documentation:
  - type: 安装指南
    path: INSTALL.md
  - type: 配置文档
    path: docs/CONFIG.md
  - type: 平台兼容性
    path: docs/PLATFORM_COMPATIBILITY.md
  - type: 排错手册
    path: docs/TROUBLESHOOTING.md
  - type: 贡献指南
    path: CONTRIBUTING.md
  - type: 更新日志
    path: CHANGELOG.md
python_requires: ">=3.8,<3.11"
commands:
  - name: 消息总结
    description: 根据指定数量总结聊天记录
    usage: /消息总结 [数量] [参数]
    parameters:
      - name: 数量
        type: integer
        required: true
        description: 要总结的消息数量
      - name: 参数
        type: string
        required: false
        description: 可选参数，如debug、语言代码等
  - name: 每日总结
    description: 自动总结每天的聊天记录
    usage: /每日总结 [开启|关闭]
    parameters:
      - name: 状态
        type: string
        required: false
        description: 开启或关闭每日总结功能
installation:
  method: source
  url: https://github.com/jokeryuyc/astrbot-enhanced-chatsummary
  script: https://raw.githubusercontent.com/jokeryuyc/astrbot-enhanced-chatsummary/main/install.sh
support:
  issues: https://github.com/jokeryuyc/astrbot-enhanced-chatsummary/issues
  email: <EMAIL>
  community: https://github.com/jokeryuyc/astrbot-enhanced-chatsummary/discussions