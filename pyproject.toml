[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "astrbot-plugin-chatsummary"
version = "1.0.3"
description = "增强版聊天记录总结插件，支持多语言和更多功能"
readme = "README.md"
requires-python = ">=3.8"
license = {file = "LICENSE"}
authors = [
    {name = "jokeryuyc", email = "<EMAIL>"},
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9", 
    "Programming Language :: Python :: 3.10",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "jinja2>=3.0.0",
    "pyyaml>=6.0.0",
    "typing-extensions>=4.0.0",
    "loguru>=0.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-asyncio>=0.16.0",
    "pytest-cov>=2.12.0",
    "black>=21.5b0",
    "flake8>=3.9.0",
    "mypy>=0.812",
]

[project.urls]
"Homepage" = "https://github.com/jokeryuyc/astrbot-enhanced-chatsummary"
"Bug Tracker" = "https://github.com/jokeryuyc/astrbot-enhanced-chatsummary/issues"
"Changelog" = "https://github.com/jokeryuyc/astrbot-enhanced-chatsummary/blob/main/CHANGELOG.md"

[project.scripts]
astrbot-summarize = "cli:main"

[tool.setuptools]
packages = ["i18n"]
py-modules = ["main", "cli"]

[tool.setuptools.package-data]
"*" = ["data/config/*.json", "data/templates/*.md", "data/templates/*.html", "i18n/*.json"]

[tool.black]
line-length = 100
target-version = ["py38", "py39", "py310"]
include = '\.pyi?$'

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
addopts = "--cov=i18n --cov=main --cov=cli --cov-report=term --cov-report=xml"
norecursedirs = ["_skip_*", "__pycache__", "*.egg-info", ".eggs", ".git", ".pytest_cache"]

[tool.coverage.run]
source = ["i18n", "main.py", "cli.py"]