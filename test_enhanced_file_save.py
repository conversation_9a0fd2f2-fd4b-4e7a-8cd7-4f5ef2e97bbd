#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的文件保存功能
包括群聊总结保存、汇总保存和周报生成
"""

import os
import asyncio
from datetime import datetime, timedelta

async def test_enhanced_save_function():
    """测试增强的保存功能"""
    
    print("🔍 测试增强的文件保存功能...")
    
    # 创建result文件夹
    result_dir = "result"
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
        print(f"✓ 创建result目录: {result_dir}")
    
    # 模拟保存函数
    async def mock_save_aggregate_to_file(content: str, save_type: str = "aggregate", group_id: str = None):
        """模拟保存函数"""
        try:
            # 生成文件名
            current_date = datetime.now().strftime("%Y-%m-%d")
            
            if save_type == "aggregate":
                filename = f"aggregate_summary_{current_date}.txt"
                header_prefix = "汇总"
            elif save_type == "group" and group_id:
                filename = f"group_summary_{current_date}_{group_id}.txt"
                header_prefix = f"群聊{group_id}总结"
            elif save_type == "weekly":
                # 获取当前周的周一日期
                today = datetime.now()
                days_since_monday = today.weekday()
                monday = today - timedelta(days=days_since_monday)
                week_date = monday.strftime("%Y-%m-%d")
                filename = f"weekly_summary_{week_date}.txt"
                header_prefix = "周报"
            else:
                filename = f"summary_{current_date}.txt"
                header_prefix = "总结"
            
            filepath = os.path.join(result_dir, filename)
            
            # 添加时间戳到内容开头
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_content = f"=== {header_prefix}时间: {timestamp} ===\n\n{content}\n\n"
            
            # 如果文件已存在，追加内容；否则创建新文件
            mode = "a" if os.path.exists(filepath) else "w"
            with open(filepath, mode, encoding="utf-8") as f:
                if mode == "a":
                    f.write("\n" + "="*50 + "\n")
                f.write(formatted_content)
            
            print(f"✓ {header_prefix} 已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            print(f"✗ 保存 {save_type} 失败: {e}")
            return None
    
    # 1. 测试群聊总结保存
    print("\n📋 1. 测试群聊总结保存...")
    group_summaries = []
    test_groups = ["123456789", "987654321", "555666777"]
    
    for group_id in test_groups:
        content = f"""【群聊 {group_id} 今日总结】
· 今天讨论了技术话题
· 分享了学习资源
· 群友互动活跃
· 整体氛围良好"""
        
        filepath = await mock_save_aggregate_to_file(content, "group", group_id)
        if filepath:
            group_summaries.append(filepath)
    
    # 2. 测试汇总保存
    print("\n📊 2. 测试汇总保存...")
    aggregate_content = """【今日群聊汇总】
今天各群聊都很活跃，讨论了多个有趣的话题。

【各群动态】
=== 群聊 123456789 ===
· 讨论了技术问题
· 分享了学习资源

=== 群聊 987654321 ===
· 聊了日常生活
· 分享了美食照片

=== 群聊 555666777 ===
· 交流了工作经验
· 讨论了行业动态

【今日总览】
整体氛围轻松愉快，大家互动积极。"""
    
    aggregate_file = await mock_save_aggregate_to_file(aggregate_content, "aggregate")
    
    # 3. 测试周报保存
    print("\n📅 3. 测试周报保存...")
    weekly_content = """【本周群聊周报】

【本周概览】
本周各群聊整体活跃度较高，讨论话题丰富多样。

【热门话题回顾】
· 技术分享和学习资源交流
· 日常生活和美食分享
· 工作经验和行业动态讨论

【各群表现】
· 群聊 123456789：技术讨论为主，学习氛围浓厚
· 群聊 987654321：生活化内容较多，互动频繁
· 群聊 555666777：职场话题丰富，经验分享活跃

【趋势分析】
相比上周，技术类话题讨论增加，群友参与度提升。

【下周展望】
预计下周将继续保持活跃，建议增加更多互动活动。"""
    
    weekly_file = await mock_save_aggregate_to_file(weekly_content, "weekly")
    
    # 4. 验证文件内容
    print("\n🔍 4. 验证保存的文件...")
    all_files = group_summaries + ([aggregate_file] if aggregate_file else []) + ([weekly_file] if weekly_file else [])
    
    for filepath in all_files:
        if filepath and os.path.exists(filepath):
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()
            print(f"✓ 文件 {os.path.basename(filepath)} 大小: {len(content)} 字符")
        else:
            print(f"✗ 文件不存在: {filepath}")
    
    return True

async def test_weekly_generation_logic():
    """测试周报生成逻辑"""
    print("\n📊 测试周报生成逻辑...")
    
    # 模拟创建上周的群聊总结文件
    result_dir = "result"
    today = datetime.now()
    days_since_monday = today.weekday()
    this_monday = today - timedelta(days=days_since_monday)
    last_monday = this_monday - timedelta(days=7)
    
    test_groups = ["123456789", "987654321"]
    
    print(f"✓ 模拟创建上周 ({last_monday.strftime('%Y-%m-%d')} 开始) 的群聊总结文件...")
    
    for group_id in test_groups:
        for i in range(7):  # 创建一周的文件
            date = last_monday + timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")
            filename = f"group_summary_{date_str}_{group_id}.txt"
            filepath = os.path.join(result_dir, filename)
            
            content = f"""=== 群聊{group_id}总结时间: {date_str} 10:00:00 ===

【群聊 {group_id} 总结 - {date_str}】
· 今天的讨论话题
· 群友互动情况
· 重要信息分享
· 整体活跃度评价

"""
            
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)
            
            print(f"  ✓ 创建文件: {filename}")
    
    print("✓ 模拟文件创建完成")
    
    # 检查周一逻辑
    if today.weekday() == 0:
        print("✓ 今天是周一，周报生成逻辑会被触发")
    else:
        print(f"ℹ️ 今天是周{today.weekday() + 1}，不是周一，但可以测试周报生成逻辑")
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始测试增强的文件保存功能...\n")
    
    try:
        # 测试基本保存功能
        success1 = await test_enhanced_save_function()
        
        # 测试周报生成逻辑
        success2 = await test_weekly_generation_logic()
        
        if success1 and success2:
            print("\n🎉 所有测试通过！")
            print("\n📋 功能特点:")
            print("   ✅ 支持多种保存类型（群聊、汇总、周报）")
            print("   ✅ 按日期和群ID命名文件")
            print("   ✅ 自动创建result目录")
            print("   ✅ 添加时间戳和类型标识")
            print("   ✅ 支持同日多次追加")
            print("   ✅ 周报生成逻辑（周一触发）")
            
            print("\n📁 文件命名规则:")
            print("   - 群聊总结: group_summary_YYYY-MM-DD_群ID.txt")
            print("   - 汇总报告: aggregate_summary_YYYY-MM-DD.txt")
            print("   - 周报: weekly_summary_YYYY-MM-DD.txt (周一日期)")
            
            print("\n🔄 工作流程:")
            print("   1. 每个群总结后 → 保存群聊总结文件")
            print("   2. 所有群处理完 → 生成并保存汇总文件")
            print("   3. 如果是周一 → 读取上周文件生成周报")
            
        else:
            print("\n❌ 部分测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
