#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试汇总结果文件保存功能
"""

import os
import asyncio
from datetime import datetime

async def test_save_function():
    """测试保存功能"""
    
    # 模拟汇总内容
    test_content = """【今日群聊汇总】
今天各群聊都很活跃，讨论了多个有趣的话题。

【各群动态】
=== 群聊 123456789 ===
· 讨论了技术问题
· 分享了学习资源

=== 群聊 987654321 ===
· 聊了日常生活
· 分享了美食照片

【今日总览】
整体氛围轻松愉快，大家互动积极。"""

    # 创建result文件夹
    result_dir = "result"
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
        print(f"✓ 创建result目录: {result_dir}")
    
    # 生成文件名（按日期）
    current_date = datetime.now().strftime("%Y-%m-%d")
    filename = f"aggregate_summary_{current_date}.txt"
    filepath = os.path.join(result_dir, filename)
    
    # 添加时间戳到内容开头
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    content = f"=== 汇总时间: {timestamp} ===\n\n{test_content}\n\n"
    
    # 如果文件已存在，追加内容；否则创建新文件
    mode = "a" if os.path.exists(filepath) else "w"
    with open(filepath, mode, encoding="utf-8") as f:
        if mode == "a":
            f.write("\n" + "="*50 + "\n")
        f.write(content)
    
    print(f"✓ 汇总结果已保存到: {filepath}")
    
    # 验证文件内容
    if os.path.exists(filepath):
        with open(filepath, "r", encoding="utf-8") as f:
            saved_content = f.read()
        print(f"✓ 文件大小: {len(saved_content)} 字符")
        print(f"✓ 文件内容预览:")
        print("-" * 40)
        print(saved_content[:200] + "..." if len(saved_content) > 200 else saved_content)
        print("-" * 40)
    
    return True

async def main():
    """主测试函数"""
    print("🚀 开始测试汇总文件保存功能...\n")
    
    try:
        success = await test_save_function()
        
        if success:
            print("\n🎉 文件保存功能测试通过！")
            print("\n📋 功能特点:")
            print("   ✅ 按日期命名文件")
            print("   ✅ 自动创建result目录")
            print("   ✅ 添加时间戳")
            print("   ✅ 支持同日多次追加")
            print("   ✅ UTF-8编码保存")
            
            print("\n📁 文件格式:")
            print("   - 文件名: aggregate_summary_YYYY-MM-DD.txt")
            print("   - 位置: ./result/")
            print("   - 编码: UTF-8")
            print("   - 追加模式: 同日多次汇总会追加到同一文件")
            
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
